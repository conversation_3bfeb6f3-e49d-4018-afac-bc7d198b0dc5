# TomatoGuard Environment Variables

# Django Settings
DEBUG=True
SECRET_KEY=your-secret-key-here

# Gemini API Configuration
GEMINI_API_KEY=AIzaSyBD15s-m0ClELhAR7XbbVPRkSFlQzcu_fQ

# Database Configuration (if using PostgreSQL)
# DATABASE_URL=postgresql://username:password@localhost:5432/tomatoguard

# Email Configuration (for contact forms)
# EMAIL_HOST=smtp.gmail.com
# EMAIL_PORT=587
# EMAIL_USE_TLS=True
# EMAIL_HOST_USER=<EMAIL>
# EMAIL_HOST_PASSWORD=your-app-password

# File Upload Settings
MAX_IMAGE_SIZE=5242880  # 5MB in bytes
ALLOWED_IMAGE_EXTENSIONS=.jpg,.jpeg,.png,.bmp,.tiff

# Logging Configuration
LOG_LEVEL=INFO
