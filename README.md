# TomatoGuard 🍅

A modern, AI-powered tomato leaf disease detection system built with Django, Tailwind CSS, HTMX, Alpine.js, and Google Gemini AI.

## Features

- **AI-Powered Disease Detection**: Uses Google Gemini API for accurate tomato disease identification
- **Modern UI**: Built with Tailwind CSS, HTMX, and Alpine.js for a responsive and interactive experience
- **Real-time Analysis**: Upload images and get instant AI-powered analysis results
- **Comprehensive Disease Database**: Supports detection of 8+ common tomato diseases
- **Detailed Recommendations**: Provides treatment and prevention suggestions
- **Mobile-Friendly**: Responsive design works on all devices
- **Admin Dashboard**: Django admin interface for managing analysis results

## Supported Diseases

- Early Blight (Alternaria solani)
- Late Blight (Phytophthora infestans)
- Leaf Mold (Passalora fulva)
- Septoria Leaf Spot (Septoria lycopersici)
- Bacterial Spot (Xanthomonas species)
- Target Spot (Corynespora cassiicola)
- Tomato Mosaic Virus
- Yellow Leaf Curl Virus
- Healthy leaf detection

## Technology Stack

- **Backend**: Django 4.2+
- **Frontend**: Tailwind CSS (CDN), HTMX, Alpine.js
- **AI**: Google Gemini API
- **Database**: SQLite (development), PostgreSQL (production ready)
- **Image Processing**: Pillow (PIL)
- **Optional**: Three.js for 3D visualizations

## Project Structure

```
TomatoGuard/
├── TomatoGuard/                 # Django project settings
│   ├── settings.py
│   ├── urls.py
│   └── wsgi.py
├── analyzer/                    # Main Django app
│   ├── models.py               # Database models
│   ├── views.py                # View functions
│   ├── forms.py                # Django forms
│   ├── urls.py                 # URL routing
│   ├── admin.py                # Admin interface
│   └── utils/                  # Utility modules
│       ├── gemini.py           # Gemini API integration
│       └── image_processing.py # Image preprocessing
├── templates/                   # HTML templates
│   ├── base.html               # Base template
│   ├── components/             # Reusable components
│   │   ├── navbar.html
│   │   ├── modals.html
│   │   ├── upload_form.html
│   │   └── analysis_results.html
│   └── analyzer/               # App-specific templates
│       ├── index.html
│       ├── upload.html
│       └── results.html
├── static/                     # Static files
│   ├── css/
│   │   └── custom.css
│   ├── js/
│   │   └── app.js
│   ├── images/
│   │   └── samples/
│   └── fonts/
├── media/                      # User uploads
└── requirements.txt            # Python dependencies
```

## Installation

### Prerequisites

- Python 3.8+
- pip
- Google Gemini API key

### Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd TomatoGuard
   ```

2. **Create virtual environment**
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Environment configuration**
   ```bash
   cp .env.example .env
   # Edit .env file with your settings
   ```

5. **Set up Gemini API**
   - Get your API key from [Google AI Studio](https://makersuite.google.com/app/apikey)
   - Add it to your `.env` file:
     ```
     GEMINI_API_KEY=your-api-key-here
     ```

6. **Database setup**
   ```bash
   python manage.py makemigrations
   python manage.py migrate
   python manage.py createsuperuser
   ```

7. **Run the development server**
   ```bash
   python manage.py runserver
   ```

8. **Access the application**
   - Main app: http://127.0.0.1:8000/
   - Admin panel: http://127.0.0.1:8000/admin/

## Usage

### For Users

1. **Upload Image**: Navigate to the upload page and select a clear image of a tomato leaf
2. **Configure Analysis**: Choose analysis type and confidence threshold
3. **Get Results**: View AI-powered analysis results with disease identification and recommendations
4. **View History**: Access previous analysis results

### For Administrators

1. **Access Admin Panel**: Login to `/admin/` with superuser credentials
2. **View Analysis Results**: Monitor all user submissions and results
3. **Manage Disease Information**: Add or update disease information in the database

## API Integration

### Gemini API Configuration

The system uses Google Gemini API for image analysis. Key features:

- **Image Preprocessing**: Automatic image optimization for better analysis
- **Structured Prompts**: Specialized prompts for tomato disease detection
- **Error Handling**: Robust error handling and fallback mechanisms
- **Response Parsing**: Intelligent parsing of AI responses

### Image Processing

- **Validation**: File type, size, and format validation
- **Preprocessing**: Automatic resizing, format conversion, and enhancement
- **Optimization**: Efficient processing for faster analysis

## Customization

### Adding New Diseases

1. Update the disease information in `analyzer/utils/gemini.py`
2. Add disease details to the `DiseaseInfo` model via admin panel
3. Update the detection logic in the Gemini analyzer

### Styling Customization

- **Tailwind CSS**: Modify classes in templates for styling changes
- **Custom CSS**: Add custom styles in `static/css/custom.css`
- **Components**: Update reusable components in `templates/components/`

### Functionality Extensions

- **Three.js Integration**: Add 3D visualizations in templates
- **Additional AI Models**: Integrate other AI services in `utils/`
- **Export Features**: Add PDF/report generation capabilities

## Deployment

### Production Checklist

1. **Environment Variables**
   ```bash
   DEBUG=False
   SECRET_KEY=your-production-secret-key
   ALLOWED_HOSTS=your-domain.com
   ```

2. **Database Configuration**
   - Configure PostgreSQL for production
   - Update `DATABASES` setting in `settings.py`

3. **Static Files**
   ```bash
   python manage.py collectstatic
   ```

4. **Security Settings**
   - Configure HTTPS
   - Set up proper CORS headers
   - Configure CSP headers

### Deployment Options

- **Heroku**: Ready for Heroku deployment
- **DigitalOcean**: App Platform compatible
- **AWS**: EC2/Elastic Beanstalk ready
- **Docker**: Containerization support

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

- **Documentation**: Check this README and code comments
- **Issues**: Report bugs via GitHub Issues
- **Contact**: Use the contact form in the application

## Acknowledgments

- **Google Gemini AI**: For powerful image analysis capabilities
- **Django Community**: For the excellent web framework
- **Tailwind CSS**: For the utility-first CSS framework
- **HTMX & Alpine.js**: For modern frontend interactions

---

**TomatoGuard** - Protecting your tomato crops with AI-powered disease detection! 🍅🛡️
