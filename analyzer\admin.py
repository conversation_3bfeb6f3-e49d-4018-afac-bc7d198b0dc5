from django.contrib import admin
from django.utils.html import format_html
from .models import AnalysisResult, DiseaseInfo

@admin.register(AnalysisResult)
class AnalysisResultAdmin(admin.ModelAdmin):
    list_display = ['id_short', 'image_thumbnail', 'is_healthy', 'disease_name', 'confidence', 'created_at']
    list_filter = ['is_healthy', 'analysis_type', 'severity', 'created_at']
    search_fields = ['disease_name', 'description', 'notes']
    readonly_fields = ['id', 'created_at', 'updated_at', 'analysis_duration', 'gemini_response']
    ordering = ['-created_at']

    fieldsets = (
        ('Basic Information', {
            'fields': ('id', 'created_at', 'updated_at', 'image')
        }),
        ('Analysis Settings', {
            'fields': ('analysis_type', 'confidence_threshold', 'notes')
        }),
        ('Results', {
            'fields': ('is_healthy', 'disease_name', 'confidence', 'severity', 'symptoms_observed', 'description', 'recommendations')
        }),
        ('Technical Details', {
            'fields': ('analysis_duration', 'error_message', 'image_width', 'image_height', 'image_size'),
            'classes': ('collapse',)
        }),
        ('Raw Data', {
            'fields': ('gemini_response',),
            'classes': ('collapse',)
        })
    )

    def id_short(self, obj):
        return obj.id.hex[:8]
    id_short.short_description = 'ID'

    def image_thumbnail(self, obj):
        if obj.image:
            return format_html('<img src="{}" width="50" height="50" style="object-fit: cover; border-radius: 4px;" />', obj.image.url)
        return "No image"
    image_thumbnail.short_description = 'Image'

@admin.register(DiseaseInfo)
class DiseaseInfoAdmin(admin.ModelAdmin):
    list_display = ['name', 'scientific_name', 'severity_level', 'created_at']
    list_filter = ['severity_level', 'created_at']
    search_fields = ['name', 'scientific_name', 'description']
    readonly_fields = ['created_at', 'updated_at']

    fieldsets = (
        ('Basic Information', {
            'fields': ('name', 'scientific_name', 'severity_level')
        }),
        ('Description', {
            'fields': ('description', 'symptoms', 'causes')
        }),
        ('Treatment', {
            'fields': ('treatment', 'prevention')
        }),
        ('Additional Information', {
            'fields': ('common_names', 'affected_parts', 'environmental_factors'),
            'classes': ('collapse',)
        }),
        ('Metadata', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
