"""
Forms for the analyzer app.
"""

from django import forms
from django.core.exceptions import ValidationError
from django.conf import settings
from .models import AnalysisResult
from .utils.image_processing import validate_uploaded_image
import os

class ImageUploadForm(forms.ModelForm):
    """Form for uploading and analyzing tomato leaf images."""
    
    class Meta:
        model = AnalysisResult
        fields = ['image', 'analysis_type', 'confidence_threshold', 'notes']
        widgets = {
            'image': forms.FileInput(attrs={
                'class': 'hidden',
                'accept': '.jpg,.jpeg,.png,.bmp,.tiff',
                'id': 'image-input'
            }),
            'analysis_type': forms.Select(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-tomato-500 focus:border-tomato-500'
            }),
            'confidence_threshold': forms.NumberInput(attrs={
                'class': 'w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider',
                'min': '50',
                'max': '95',
                'step': '5',
                'value': '75'
            }),
            'notes': forms.Textarea(attrs={
                'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-tomato-500 focus:border-tomato-500 resize-none',
                'rows': 3,
                'placeholder': 'Describe any symptoms you\'ve noticed or specific concerns...'
            })
        }
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Set field labels
        self.fields['image'].label = 'Tomato Leaf Image'
        self.fields['analysis_type'].label = 'Analysis Type'
        self.fields['confidence_threshold'].label = 'Confidence Threshold'
        self.fields['notes'].label = 'Additional Notes (Optional)'
        
        # Set help texts
        self.fields['image'].help_text = 'Upload a clear image of your tomato leaf (JPG, PNG, BMP, TIFF - Max 5MB)'
        self.fields['analysis_type'].help_text = 'Choose the type of analysis to perform'
        self.fields['confidence_threshold'].help_text = 'Minimum confidence level for disease detection'
        self.fields['notes'].help_text = 'Any additional information about the plant or symptoms'
        
        # Make image field required
        self.fields['image'].required = True
    
    def clean_image(self):
        """Validate the uploaded image."""
        image = self.cleaned_data.get('image')
        
        if not image:
            raise ValidationError('Please select an image to upload.')
        
        # Validate using our image processing utility
        is_valid, error_message = validate_uploaded_image(image)
        if not is_valid:
            raise ValidationError(error_message)
        
        return image
    
    def clean_confidence_threshold(self):
        """Validate confidence threshold."""
        threshold = self.cleaned_data.get('confidence_threshold')
        
        if threshold is not None:
            if threshold < 50 or threshold > 95:
                raise ValidationError('Confidence threshold must be between 50% and 95%.')
        
        return threshold
    
    def clean_notes(self):
        """Clean and validate notes field."""
        notes = self.cleaned_data.get('notes', '')
        
        # Limit notes length
        if len(notes) > 1000:
            raise ValidationError('Notes must be less than 1000 characters.')
        
        return notes.strip()

class AnalysisFilterForm(forms.Form):
    """Form for filtering analysis results."""
    
    HEALTH_STATUS_CHOICES = [
        ('', 'All'),
        ('healthy', 'Healthy'),
        ('diseased', 'Diseased'),
    ]
    
    CONFIDENCE_CHOICES = [
        ('', 'All Confidence Levels'),
        ('high', 'High (80%+)'),
        ('moderate', 'Moderate (60-79%)'),
        ('low', 'Low (<60%)'),
    ]
    
    DATE_RANGE_CHOICES = [
        ('', 'All Time'),
        ('today', 'Today'),
        ('week', 'This Week'),
        ('month', 'This Month'),
        ('year', 'This Year'),
    ]
    
    health_status = forms.ChoiceField(
        choices=HEALTH_STATUS_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-tomato-500 focus:border-tomato-500'
        })
    )
    
    confidence_level = forms.ChoiceField(
        choices=CONFIDENCE_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-tomato-500 focus:border-tomato-500'
        })
    )
    
    date_range = forms.ChoiceField(
        choices=DATE_RANGE_CHOICES,
        required=False,
        widget=forms.Select(attrs={
            'class': 'px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-tomato-500 focus:border-tomato-500'
        })
    )
    
    search = forms.CharField(
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-tomato-500 focus:border-tomato-500',
            'placeholder': 'Search by disease name or description...'
        })
    )

class FeedbackForm(forms.Form):
    """Form for collecting user feedback on analysis results."""
    
    ACCURACY_CHOICES = [
        (5, 'Very Accurate'),
        (4, 'Accurate'),
        (3, 'Somewhat Accurate'),
        (2, 'Inaccurate'),
        (1, 'Very Inaccurate'),
    ]
    
    USEFULNESS_CHOICES = [
        (5, 'Very Useful'),
        (4, 'Useful'),
        (3, 'Somewhat Useful'),
        (2, 'Not Very Useful'),
        (1, 'Not Useful'),
    ]
    
    analysis_id = forms.UUIDField(widget=forms.HiddenInput())
    
    accuracy_rating = forms.ChoiceField(
        choices=ACCURACY_CHOICES,
        widget=forms.RadioSelect(attrs={
            'class': 'text-tomato-600 focus:ring-tomato-500'
        }),
        label='How accurate was the analysis?'
    )
    
    usefulness_rating = forms.ChoiceField(
        choices=USEFULNESS_CHOICES,
        widget=forms.RadioSelect(attrs={
            'class': 'text-tomato-600 focus:ring-tomato-500'
        }),
        label='How useful were the recommendations?'
    )
    
    comments = forms.CharField(
        required=False,
        widget=forms.Textarea(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-tomato-500 focus:border-tomato-500 resize-none',
            'rows': 4,
            'placeholder': 'Please share any additional feedback or suggestions...'
        }),
        label='Additional Comments (Optional)'
    )
    
    would_recommend = forms.BooleanField(
        required=False,
        widget=forms.CheckboxInput(attrs={
            'class': 'text-tomato-600 focus:ring-tomato-500 rounded'
        }),
        label='Would you recommend TomatoGuard to others?'
    )
    
    def clean_comments(self):
        """Clean and validate comments."""
        comments = self.cleaned_data.get('comments', '')
        
        if len(comments) > 2000:
            raise ValidationError('Comments must be less than 2000 characters.')
        
        return comments.strip()

class ContactForm(forms.Form):
    """Form for contacting support or providing general feedback."""
    
    SUBJECT_CHOICES = [
        ('technical', 'Technical Issue'),
        ('feedback', 'General Feedback'),
        ('feature', 'Feature Request'),
        ('question', 'Question'),
        ('other', 'Other'),
    ]
    
    name = forms.CharField(
        max_length=100,
        widget=forms.TextInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-tomato-500 focus:border-tomato-500',
            'placeholder': 'Your name'
        })
    )
    
    email = forms.EmailField(
        widget=forms.EmailInput(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-tomato-500 focus:border-tomato-500',
            'placeholder': '<EMAIL>'
        })
    )
    
    subject = forms.ChoiceField(
        choices=SUBJECT_CHOICES,
        widget=forms.Select(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-tomato-500 focus:border-tomato-500'
        })
    )
    
    message = forms.CharField(
        widget=forms.Textarea(attrs={
            'class': 'w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-tomato-500 focus:border-tomato-500 resize-none',
            'rows': 6,
            'placeholder': 'Please describe your issue or feedback in detail...'
        })
    )
    
    def clean_message(self):
        """Clean and validate message."""
        message = self.cleaned_data.get('message', '')
        
        if len(message) < 10:
            raise ValidationError('Message must be at least 10 characters long.')
        
        if len(message) > 5000:
            raise ValidationError('Message must be less than 5000 characters.')
        
        return message.strip()
