"""
Django management command to test Gemini API integration.
Usage: python manage.py test_gemini
"""

from django.core.management.base import BaseCommand
from django.conf import settings
from PIL import Image
import io
from django.core.files.uploadedfile import SimpleUploadedFile

class Command(BaseCommand):
    help = 'Test Gemini API integration with a sample image'

    def add_arguments(self, parser):
        parser.add_argument(
            '--skip-api',
            action='store_true',
            help='Skip actual API call and test only setup',
        )

    def handle(self, *args, **options):
        self.stdout.write(
            self.style.SUCCESS('🍅 Testing TomatoGuard Gemini Integration')
        )
        self.stdout.write('=' * 50)

        # Test 1: Check API key
        api_key = getattr(settings, 'GEMINI_API_KEY', None)
        if api_key:
            self.stdout.write(
                self.style.SUCCESS('✅ Gemini API key found in settings')
            )
        else:
            self.stdout.write(
                self.style.ERROR('❌ Gemini API key not found in settings')
            )
            self.stdout.write('Please add GEMINI_API_KEY to your .env file')
            return

        # Test 2: Import Gemini utilities
        try:
            from analyzer.utils.gemini import GeminiAnalyzer, analyze_tomato_leaf
            self.stdout.write(
                self.style.SUCCESS('✅ Gemini utilities imported successfully')
            )
        except ImportError as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Failed to import Gemini utilities: {e}')
            )
            return

        # Test 3: Create analyzer instance
        try:
            analyzer = GeminiAnalyzer()
            self.stdout.write(
                self.style.SUCCESS('✅ GeminiAnalyzer instance created')
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Failed to create GeminiAnalyzer: {e}')
            )
            return

        # Test 4: Create test image
        try:
            test_image = self.create_test_image()
            self.stdout.write(
                self.style.SUCCESS('✅ Test image created')
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Failed to create test image: {e}')
            )
            return

        # Test 5: Test image preprocessing
        try:
            processed_image = analyzer.preprocess_image(test_image)
            self.stdout.write(
                self.style.SUCCESS(f'✅ Image preprocessing successful: {processed_image.size}')
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'❌ Image preprocessing failed: {e}')
            )
            return

        # Test 6: Test API call (if not skipped)
        if not options['skip_api']:
            self.stdout.write('Testing actual Gemini API call...')
            try:
                test_image.seek(0)  # Reset file pointer
                result = analyze_tomato_leaf(test_image)
                
                if result.get('error'):
                    self.stdout.write(
                        self.style.ERROR(f'❌ API call failed: {result.get("error_message")}')
                    )
                else:
                    self.stdout.write(
                        self.style.SUCCESS('✅ Gemini API call successful')
                    )
                    self.stdout.write(f'   - Healthy: {result.get("is_healthy")}')
                    self.stdout.write(f'   - Confidence: {result.get("confidence", 0)}%')
                    if result.get('disease_detected'):
                        self.stdout.write(f'   - Disease: {result.get("disease_detected")}')
                    
            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f'❌ API call failed with exception: {e}')
                )
                return
        else:
            self.stdout.write(
                self.style.WARNING('⚠️  Skipping actual API call (--skip-api flag used)')
            )

        # Summary
        self.stdout.write('=' * 50)
        self.stdout.write(
            self.style.SUCCESS('🎉 All tests completed successfully!')
        )
        self.stdout.write('\nYour TomatoGuard setup is ready for use.')
        self.stdout.write('\nNext steps:')
        self.stdout.write('1. Run: python manage.py runserver')
        self.stdout.write('2. Visit: http://127.0.0.1:8000/upload/')
        self.stdout.write('3. Upload a tomato leaf image for analysis')

    def create_test_image(self):
        """Create a simple test image that looks like a leaf."""
        # Create a green image that could represent a leaf
        img = Image.new('RGB', (400, 300), color=(34, 139, 34))  # Forest green
        
        # Add some variation to make it more leaf-like
        pixels = img.load()
        for i in range(img.width):
            for j in range(img.height):
                # Add some random variation
                r, g, b = pixels[i, j]
                # Vary the green slightly
                g = max(0, min(255, g + (i + j) % 40 - 20))
                pixels[i, j] = (r, g, b)
        
        # Save to bytes
        img_bytes = io.BytesIO()
        img.save(img_bytes, format='JPEG', quality=85)
        img_bytes.seek(0)
        
        return SimpleUploadedFile(
            name='test_tomato_leaf.jpg',
            content=img_bytes.read(),
            content_type='image/jpeg'
        )
