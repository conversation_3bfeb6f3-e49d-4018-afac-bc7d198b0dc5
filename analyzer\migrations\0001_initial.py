# Generated by Django 4.2 on 2025-05-30 15:15

import analyzer.models
import django.core.validators
from django.db import migrations, models
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='AnalysisResult',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('image', models.ImageField(max_length=500, upload_to=analyzer.models.upload_to_analysis)),
                ('analysis_type', models.CharField(choices=[('disease_detection', 'Disease Detection'), ('health_assessment', 'Health Assessment'), ('detailed_analysis', 'Detailed Analysis')], default='disease_detection', max_length=20)),
                ('confidence_threshold', models.IntegerField(default=75, validators=[django.core.validators.MinValueValidator(50), django.core.validators.MaxValueValidator(95)])),
                ('notes', models.TextField(blank=True, help_text='Additional notes provided by user')),
                ('is_healthy', models.BooleanField(blank=True, null=True)),
                ('disease_name', models.CharField(blank=True, max_length=100, null=True)),
                ('confidence', models.IntegerField(default=0, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('severity', models.CharField(choices=[('low', 'Low'), ('moderate', 'Moderate'), ('high', 'High'), ('unknown', 'Unknown')], default='unknown', max_length=10)),
                ('symptoms_observed', models.JSONField(blank=True, default=list)),
                ('description', models.TextField(blank=True)),
                ('recommendations', models.TextField(blank=True)),
                ('analysis_duration', models.FloatField(blank=True, help_text='Analysis time in seconds', null=True)),
                ('gemini_response', models.JSONField(blank=True, default=dict, help_text='Raw Gemini API response')),
                ('error_message', models.TextField(blank=True, null=True)),
                ('image_width', models.IntegerField(blank=True, null=True)),
                ('image_height', models.IntegerField(blank=True, null=True)),
                ('image_size', models.IntegerField(blank=True, help_text='File size in bytes', null=True)),
            ],
            options={
                'verbose_name': 'Analysis Result',
                'verbose_name_plural': 'Analysis Results',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DiseaseInfo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('scientific_name', models.CharField(blank=True, max_length=150)),
                ('description', models.TextField()),
                ('symptoms', models.TextField()),
                ('causes', models.TextField()),
                ('treatment', models.TextField()),
                ('prevention', models.TextField()),
                ('severity_level', models.CharField(choices=[('low', 'Low'), ('moderate', 'Moderate'), ('high', 'High'), ('unknown', 'Unknown')], default='moderate', max_length=10)),
                ('common_names', models.JSONField(blank=True, default=list)),
                ('affected_parts', models.JSONField(blank=True, default=list)),
                ('environmental_factors', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Disease Information',
                'verbose_name_plural': 'Disease Information',
                'ordering': ['name'],
            },
        ),
    ]
