from django.db import models
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
import uuid
import os

def upload_to_analysis(instance, filename):
    """Generate upload path for analysis images."""
    ext = filename.split('.')[-1]
    filename = f"{uuid.uuid4()}.{ext}"
    return os.path.join('analysis_images', timezone.now().strftime('%Y/%m/%d'), filename)

class AnalysisResult(models.Model):
    """Model to store tomato leaf analysis results."""

    ANALYSIS_TYPES = [
        ('disease_detection', 'Disease Detection'),
        ('health_assessment', 'Health Assessment'),
        ('detailed_analysis', 'Detailed Analysis'),
    ]

    SEVERITY_LEVELS = [
        ('low', 'Low'),
        ('moderate', 'Moderate'),
        ('high', 'High'),
        ('unknown', 'Unknown'),
    ]

    # Basic information
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Image and analysis settings
    image = models.ImageField(upload_to=upload_to_analysis, max_length=500)
    analysis_type = models.CharField(max_length=20, choices=ANALYSIS_TYPES, default='disease_detection')
    confidence_threshold = models.IntegerField(
        default=75,
        validators=[MinValueValidator(50), MaxValueValidator(95)]
    )
    notes = models.TextField(blank=True, help_text="Additional notes provided by user")

    # Analysis results
    is_healthy = models.BooleanField(null=True, blank=True)
    disease_name = models.CharField(max_length=100, blank=True, null=True)
    confidence = models.IntegerField(
        default=0,
        validators=[MinValueValidator(0), MaxValueValidator(100)]
    )
    severity = models.CharField(max_length=10, choices=SEVERITY_LEVELS, default='unknown')

    # Detailed results
    symptoms_observed = models.JSONField(default=list, blank=True)
    description = models.TextField(blank=True)
    recommendations = models.TextField(blank=True)

    # Technical details
    analysis_duration = models.FloatField(null=True, blank=True, help_text="Analysis time in seconds")
    gemini_response = models.JSONField(default=dict, blank=True, help_text="Raw Gemini API response")
    error_message = models.TextField(blank=True, null=True)

    # Image metadata
    image_width = models.IntegerField(null=True, blank=True)
    image_height = models.IntegerField(null=True, blank=True)
    image_size = models.IntegerField(null=True, blank=True, help_text="File size in bytes")

    class Meta:
        ordering = ['-created_at']
        verbose_name = "Analysis Result"
        verbose_name_plural = "Analysis Results"

    def __str__(self):
        status = "Healthy" if self.is_healthy else f"Disease: {self.disease_name}"
        return f"Analysis {self.id.hex[:8]} - {status} ({self.confidence}%)"

    @property
    def is_high_confidence(self):
        """Check if the analysis has high confidence."""
        return self.confidence >= 80

    @property
    def is_low_confidence(self):
        """Check if the analysis has low confidence."""
        return self.confidence < self.confidence_threshold

    @property
    def confidence_level(self):
        """Get human-readable confidence level."""
        if self.confidence >= 80:
            return "High"
        elif self.confidence >= 60:
            return "Moderate"
        else:
            return "Low"

    @property
    def status_color(self):
        """Get color class for status display."""
        if self.is_healthy:
            return "green"
        elif self.severity == "high":
            return "red"
        elif self.severity == "moderate":
            return "yellow"
        else:
            return "orange"

    def get_image_info(self):
        """Get formatted image information."""
        if self.image:
            return {
                'filename': os.path.basename(self.image.name),
                'size': f"{self.image_width}x{self.image_height}" if self.image_width and self.image_height else "Unknown",
                'file_size': self._format_file_size(self.image_size) if self.image_size else "Unknown",
                'url': self.image.url
            }
        return None

    def _format_file_size(self, size_bytes):
        """Format file size in human-readable format."""
        if size_bytes == 0:
            return "0 B"

        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1

        return f"{size_bytes:.1f} {size_names[i]}"

class DiseaseInfo(models.Model):
    """Model to store information about tomato diseases."""

    name = models.CharField(max_length=100, unique=True)
    scientific_name = models.CharField(max_length=150, blank=True)
    description = models.TextField()
    symptoms = models.TextField()
    causes = models.TextField()
    treatment = models.TextField()
    prevention = models.TextField()
    severity_level = models.CharField(max_length=10, choices=AnalysisResult.SEVERITY_LEVELS, default='moderate')

    # Additional information
    common_names = models.JSONField(default=list, blank=True)
    affected_parts = models.JSONField(default=list, blank=True)  # leaves, stems, fruits, etc.
    environmental_factors = models.TextField(blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['name']
        verbose_name = "Disease Information"
        verbose_name_plural = "Disease Information"

    def __str__(self):
        return self.name
