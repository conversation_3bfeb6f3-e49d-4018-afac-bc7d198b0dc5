"""
Gemini API integration for tomato leaf disease detection.
"""

import os
import base64
import json
import logging
from typing import Dict, Any, Optional, Tuple
from PIL import Image
import google.generativeai as genai
from django.conf import settings
from django.core.files.uploadedfile import InMemoryUploadedFile

# Configure logging
logger = logging.getLogger(__name__)

class GeminiAnalyzer:
    """
    Handles image analysis using Google Gemini API for tomato disease detection.
    """
    
    def __init__(self):
        """Initialize the Gemini analyzer with API configuration."""
        self.api_key = getattr(settings, 'GEMINI_API_KEY', None)
        if not self.api_key:
            raise ValueError("GEMINI_API_KEY not found in settings")
        
        # Configure Gemini
        genai.configure(api_key=self.api_key)
        self.model = genai.GenerativeModel('gemini-1.5-flash')
        
        # Disease information database
        self.disease_info = {
            'early_blight': {
                'name': 'Early Blight',
                'description': 'A fungal disease caused by Alternaria solani that affects tomato leaves, stems, and fruits.',
                'symptoms': 'Dark brown spots with concentric rings, yellowing leaves, defoliation',
                'treatment': 'Apply fungicides, improve air circulation, remove infected plant debris',
                'prevention': 'Crop rotation, resistant varieties, proper spacing, avoid overhead watering'
            },
            'late_blight': {
                'name': 'Late Blight',
                'description': 'A serious fungal disease caused by Phytophthora infestans that can destroy entire crops.',
                'symptoms': 'Water-soaked spots, white fuzzy growth on leaf undersides, rapid plant death',
                'treatment': 'Immediate fungicide application, remove infected plants, improve drainage',
                'prevention': 'Use resistant varieties, ensure good air circulation, avoid wet conditions'
            },
            'leaf_mold': {
                'name': 'Leaf Mold',
                'description': 'A fungal disease caused by Passalora fulva that thrives in humid conditions.',
                'symptoms': 'Yellow spots on upper leaf surface, olive-green mold on undersides',
                'treatment': 'Reduce humidity, improve ventilation, apply appropriate fungicides',
                'prevention': 'Control humidity, ensure proper spacing, use resistant varieties'
            },
            'septoria_leaf_spot': {
                'name': 'Septoria Leaf Spot',
                'description': 'A fungal disease caused by Septoria lycopersici affecting tomato foliage.',
                'symptoms': 'Small circular spots with dark borders and light centers, yellowing leaves',
                'treatment': 'Apply fungicides, remove infected leaves, improve air circulation',
                'prevention': 'Crop rotation, mulching, avoid overhead watering'
            },
            'bacterial_spot': {
                'name': 'Bacterial Spot',
                'description': 'A bacterial disease caused by Xanthomonas species affecting leaves and fruits.',
                'symptoms': 'Small dark spots with yellow halos, leaf drop, fruit lesions',
                'treatment': 'Copper-based bactericides, remove infected plants, improve sanitation',
                'prevention': 'Use pathogen-free seeds, avoid overhead irrigation, crop rotation'
            },
            'target_spot': {
                'name': 'Target Spot',
                'description': 'A fungal disease caused by Corynespora cassiicola with distinctive target-like lesions.',
                'symptoms': 'Circular spots with concentric rings resembling targets',
                'treatment': 'Fungicide applications, remove infected debris, improve air flow',
                'prevention': 'Resistant varieties, proper plant spacing, avoid wet foliage'
            },
            'mosaic_virus': {
                'name': 'Tomato Mosaic Virus',
                'description': 'A viral disease causing mottled patterns and stunted growth.',
                'symptoms': 'Mottled light and dark green patterns, stunted growth, distorted leaves',
                'treatment': 'No cure available, remove infected plants, control aphid vectors',
                'prevention': 'Use virus-free seeds, control aphids, practice good sanitation'
            },
            'yellow_leaf_curl': {
                'name': 'Tomato Yellow Leaf Curl Virus',
                'description': 'A viral disease transmitted by whiteflies causing leaf curling and yellowing.',
                'symptoms': 'Upward curling of leaves, yellowing, stunted growth',
                'treatment': 'Remove infected plants, control whitefly populations',
                'prevention': 'Use resistant varieties, control whiteflies, reflective mulches'
            }
        }
    
    def preprocess_image(self, image_file: InMemoryUploadedFile) -> Image.Image:
        """
        Preprocess the uploaded image for analysis.
        
        Args:
            image_file: Uploaded image file
            
        Returns:
            PIL Image object
        """
        try:
            # Open and convert image
            image = Image.open(image_file)
            
            # Convert to RGB if necessary
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # Resize if too large (max 1024x1024 for efficiency)
            max_size = 1024
            if max(image.size) > max_size:
                image.thumbnail((max_size, max_size), Image.Resampling.LANCZOS)
            
            logger.info(f"Image preprocessed: {image.size}, mode: {image.mode}")
            return image
            
        except Exception as e:
            logger.error(f"Error preprocessing image: {str(e)}")
            raise ValueError(f"Invalid image file: {str(e)}")
    
    def create_analysis_prompt(self, analysis_type: str = 'disease_detection') -> str:
        """
        Create a detailed prompt for Gemini analysis.
        
        Args:
            analysis_type: Type of analysis to perform
            
        Returns:
            Formatted prompt string
        """
        base_prompt = """
        You are an expert plant pathologist specializing in tomato diseases. Analyze this tomato leaf image and provide a detailed assessment.

        Please examine the image for:
        1. Overall plant health status
        2. Signs of diseases or disorders
        3. Specific symptoms visible
        4. Confidence level in your assessment

        Common tomato diseases to look for:
        - Early Blight (Alternaria solani): Dark spots with concentric rings
        - Late Blight (Phytophthora infestans): Water-soaked spots, white fuzzy growth
        - Leaf Mold (Passalora fulva): Yellow spots with olive-green mold underneath
        - Septoria Leaf Spot: Small circular spots with dark borders
        - Bacterial Spot: Small dark spots with yellow halos
        - Target Spot: Circular spots with concentric rings
        - Mosaic Virus: Mottled light and dark green patterns
        - Yellow Leaf Curl Virus: Upward curling and yellowing

        Respond in JSON format with the following structure:
        {
            "is_healthy": boolean,
            "disease_detected": "disease_name or null",
            "confidence": integer (0-100),
            "symptoms_observed": ["list", "of", "symptoms"],
            "severity": "low/moderate/high",
            "description": "detailed description of findings",
            "recommendations": "treatment and prevention recommendations"
        }

        Be precise and scientific in your analysis. If you're uncertain, indicate lower confidence and suggest professional consultation.
        """
        
        if analysis_type == 'detailed_analysis':
            base_prompt += """
            
            Additionally provide:
            - Detailed symptom analysis
            - Possible environmental factors
            - Stage of disease progression
            - Prognosis if treated/untreated
            """
        
        return base_prompt.strip()
    
    def analyze_image(self, image_file: InMemoryUploadedFile, 
                     analysis_type: str = 'disease_detection',
                     confidence_threshold: int = 75) -> Dict[str, Any]:
        """
        Analyze tomato leaf image using Gemini API.
        
        Args:
            image_file: Uploaded image file
            analysis_type: Type of analysis to perform
            confidence_threshold: Minimum confidence threshold
            
        Returns:
            Dictionary containing analysis results
        """
        try:
            # Preprocess image
            image = self.preprocess_image(image_file)
            
            # Create prompt
            prompt = self.create_analysis_prompt(analysis_type)
            
            # Analyze with Gemini
            logger.info("Sending image to Gemini for analysis...")
            response = self.model.generate_content([prompt, image])
            
            # Parse response
            result = self._parse_gemini_response(response.text)
            
            # Enhance with additional information
            result = self._enhance_analysis_result(result, confidence_threshold)
            
            logger.info(f"Analysis completed: {result.get('disease_detected', 'healthy')}")
            return result
            
        except Exception as e:
            logger.error(f"Error during image analysis: {str(e)}")
            return self._create_error_result(str(e))
    
    def _parse_gemini_response(self, response_text: str) -> Dict[str, Any]:
        """
        Parse Gemini API response text into structured data.
        
        Args:
            response_text: Raw response from Gemini
            
        Returns:
            Parsed analysis result
        """
        try:
            # Try to extract JSON from response
            start_idx = response_text.find('{')
            end_idx = response_text.rfind('}') + 1
            
            if start_idx != -1 and end_idx != 0:
                json_str = response_text[start_idx:end_idx]
                result = json.loads(json_str)
                
                # Validate required fields
                required_fields = ['is_healthy', 'confidence', 'description']
                for field in required_fields:
                    if field not in result:
                        raise ValueError(f"Missing required field: {field}")
                
                return result
            else:
                raise ValueError("No valid JSON found in response")
                
        except (json.JSONDecodeError, ValueError) as e:
            logger.error(f"Error parsing Gemini response: {str(e)}")
            # Fallback: create basic result from text
            return self._create_fallback_result(response_text)
    
    def _create_fallback_result(self, response_text: str) -> Dict[str, Any]:
        """
        Create a fallback result when JSON parsing fails.
        
        Args:
            response_text: Raw response text
            
        Returns:
            Basic analysis result
        """
        # Simple keyword-based analysis as fallback
        text_lower = response_text.lower()
        
        diseases = ['blight', 'mold', 'spot', 'virus', 'bacterial', 'fungal']
        disease_detected = any(disease in text_lower for disease in diseases)
        
        return {
            'is_healthy': not disease_detected,
            'disease_detected': 'Unknown Disease' if disease_detected else None,
            'confidence': 50,  # Low confidence for fallback
            'symptoms_observed': [],
            'severity': 'unknown',
            'description': response_text[:500] + '...' if len(response_text) > 500 else response_text,
            'recommendations': 'Please consult with a plant pathologist for accurate diagnosis.'
        }
    
    def _enhance_analysis_result(self, result: Dict[str, Any], 
                                confidence_threshold: int) -> Dict[str, Any]:
        """
        Enhance analysis result with additional information.
        
        Args:
            result: Basic analysis result
            confidence_threshold: Minimum confidence threshold
            
        Returns:
            Enhanced analysis result
        """
        # Add disease information if available
        disease_name = result.get('disease_detected')
        if disease_name and not result.get('is_healthy', True):
            # Try to match with known diseases
            disease_key = self._match_disease_name(disease_name)
            if disease_key and disease_key in self.disease_info:
                disease_info = self.disease_info[disease_key]
                result['disease_name'] = disease_info['name']
                if not result.get('recommendations'):
                    result['recommendations'] = f"{disease_info['treatment']}. Prevention: {disease_info['prevention']}"
        
        # Adjust confidence based on threshold
        if result.get('confidence', 0) < confidence_threshold:
            result['low_confidence_warning'] = True
            result['recommendations'] = (result.get('recommendations', '') + 
                                       ' Note: Low confidence result. Consider professional consultation.').strip()
        
        # Add timestamp
        from datetime import datetime
        result['analyzed_at'] = datetime.now().isoformat()
        
        return result
    
    def _match_disease_name(self, disease_name: str) -> Optional[str]:
        """
        Match detected disease name with known disease keys.
        
        Args:
            disease_name: Detected disease name
            
        Returns:
            Matching disease key or None
        """
        disease_name_lower = disease_name.lower()
        
        # Simple keyword matching
        if 'early' in disease_name_lower and 'blight' in disease_name_lower:
            return 'early_blight'
        elif 'late' in disease_name_lower and 'blight' in disease_name_lower:
            return 'late_blight'
        elif 'leaf' in disease_name_lower and 'mold' in disease_name_lower:
            return 'leaf_mold'
        elif 'septoria' in disease_name_lower:
            return 'septoria_leaf_spot'
        elif 'bacterial' in disease_name_lower and 'spot' in disease_name_lower:
            return 'bacterial_spot'
        elif 'target' in disease_name_lower and 'spot' in disease_name_lower:
            return 'target_spot'
        elif 'mosaic' in disease_name_lower:
            return 'mosaic_virus'
        elif 'yellow' in disease_name_lower and 'curl' in disease_name_lower:
            return 'yellow_leaf_curl'
        
        return None
    
    def _create_error_result(self, error_message: str) -> Dict[str, Any]:
        """
        Create an error result when analysis fails.
        
        Args:
            error_message: Error description
            
        Returns:
            Error result dictionary
        """
        return {
            'error': True,
            'error_message': error_message,
            'is_healthy': None,
            'confidence': 0,
            'description': f'Analysis failed: {error_message}',
            'recommendations': 'Please try again with a different image or contact support.'
        }

# Convenience function for quick analysis
def analyze_tomato_leaf(image_file: InMemoryUploadedFile, 
                       analysis_type: str = 'disease_detection',
                       confidence_threshold: int = 75) -> Dict[str, Any]:
    """
    Quick function to analyze a tomato leaf image.
    
    Args:
        image_file: Uploaded image file
        analysis_type: Type of analysis to perform
        confidence_threshold: Minimum confidence threshold
        
    Returns:
        Analysis result dictionary
    """
    analyzer = GeminiAnalyzer()
    return analyzer.analyze_image(image_file, analysis_type, confidence_threshold)
