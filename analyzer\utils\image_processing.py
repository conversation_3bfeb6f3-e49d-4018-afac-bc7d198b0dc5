"""
Image processing utilities for tomato leaf analysis.
"""

import os
import logging
from typing import <PERSON><PERSON>, Optional
from PIL import Image, ImageEnhance, ImageFilter
from django.core.files.uploadedfile import InMemoryUploadedFile
from django.conf import settings
import io

# Configure logging
logger = logging.getLogger(__name__)

class ImageProcessor:
    """
    Handles image preprocessing and enhancement for better analysis results.
    """
    
    def __init__(self):
        """Initialize the image processor with default settings."""
        self.max_size = getattr(settings, 'MAX_IMAGE_SIZE', 1024)
        self.quality = 85
        self.allowed_formats = ['JPEG', 'PNG', 'BMP', 'TIFF']
    
    def validate_image(self, image_file: InMemoryUploadedFile) -> Tuple[bool, str]:
        """
        Validate uploaded image file.
        
        Args:
            image_file: Uploaded image file
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        try:
            # Check file size
            max_size = getattr(settings, 'MAX_IMAGE_SIZE', 5 * 1024 * 1024)  # 5MB default
            if image_file.size > max_size:
                return False, f"File size ({self._format_file_size(image_file.size)}) exceeds maximum allowed size ({self._format_file_size(max_size)})"
            
            # Check file type
            allowed_extensions = getattr(settings, 'ALLOWED_IMAGE_EXTENSIONS', ['.jpg', '.jpeg', '.png', '.bmp', '.tiff'])
            file_extension = os.path.splitext(image_file.name)[1].lower()
            if file_extension not in allowed_extensions:
                return False, f"File type '{file_extension}' not allowed. Supported formats: {', '.join(allowed_extensions)}"
            
            # Try to open and validate image
            try:
                image = Image.open(image_file)
                image.verify()  # Verify it's a valid image
                
                # Reset file pointer after verify()
                image_file.seek(0)
                
                # Check image format
                image = Image.open(image_file)
                if image.format not in self.allowed_formats:
                    return False, f"Image format '{image.format}' not supported"
                
                # Check image dimensions
                if image.size[0] < 100 or image.size[1] < 100:
                    return False, "Image too small. Minimum size is 100x100 pixels"
                
                if image.size[0] > 4000 or image.size[1] > 4000:
                    return False, "Image too large. Maximum size is 4000x4000 pixels"
                
                # Reset file pointer
                image_file.seek(0)
                
                return True, "Valid image"
                
            except Exception as e:
                return False, f"Invalid image file: {str(e)}"
                
        except Exception as e:
            logger.error(f"Error validating image: {str(e)}")
            return False, f"Error validating image: {str(e)}"
    
    def preprocess_image(self, image_file: InMemoryUploadedFile, 
                        enhance: bool = True) -> Image.Image:
        """
        Preprocess image for optimal analysis.
        
        Args:
            image_file: Uploaded image file
            enhance: Whether to apply image enhancements
            
        Returns:
            Processed PIL Image
        """
        try:
            # Open image
            image = Image.open(image_file)
            
            # Convert to RGB if necessary
            if image.mode != 'RGB':
                if image.mode == 'RGBA':
                    # Create white background for transparent images
                    background = Image.new('RGB', image.size, (255, 255, 255))
                    background.paste(image, mask=image.split()[-1] if image.mode == 'RGBA' else None)
                    image = background
                else:
                    image = image.convert('RGB')
            
            # Resize if too large
            if max(image.size) > self.max_size:
                image = self._resize_image(image, self.max_size)
            
            # Apply enhancements if requested
            if enhance:
                image = self._enhance_image(image)
            
            logger.info(f"Image preprocessed: {image.size}, mode: {image.mode}")
            return image
            
        except Exception as e:
            logger.error(f"Error preprocessing image: {str(e)}")
            raise ValueError(f"Failed to preprocess image: {str(e)}")
    
    def _resize_image(self, image: Image.Image, max_size: int) -> Image.Image:
        """
        Resize image while maintaining aspect ratio.
        
        Args:
            image: PIL Image to resize
            max_size: Maximum dimension size
            
        Returns:
            Resized PIL Image
        """
        # Calculate new size maintaining aspect ratio
        width, height = image.size
        if width > height:
            new_width = max_size
            new_height = int((height * max_size) / width)
        else:
            new_height = max_size
            new_width = int((width * max_size) / height)
        
        # Use high-quality resampling
        resized_image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
        logger.info(f"Image resized from {image.size} to {resized_image.size}")
        
        return resized_image
    
    def _enhance_image(self, image: Image.Image) -> Image.Image:
        """
        Apply image enhancements for better analysis.
        
        Args:
            image: PIL Image to enhance
            
        Returns:
            Enhanced PIL Image
        """
        try:
            # Enhance contrast slightly
            enhancer = ImageEnhance.Contrast(image)
            image = enhancer.enhance(1.1)
            
            # Enhance color saturation slightly
            enhancer = ImageEnhance.Color(image)
            image = enhancer.enhance(1.05)
            
            # Enhance sharpness slightly
            enhancer = ImageEnhance.Sharpness(image)
            image = enhancer.enhance(1.1)
            
            # Apply slight noise reduction
            image = image.filter(ImageFilter.MedianFilter(size=3))
            
            logger.info("Image enhancements applied")
            return image
            
        except Exception as e:
            logger.warning(f"Error applying image enhancements: {str(e)}")
            return image  # Return original if enhancement fails
    
    def create_thumbnail(self, image: Image.Image, size: Tuple[int, int] = (200, 200)) -> Image.Image:
        """
        Create a thumbnail of the image.
        
        Args:
            image: PIL Image to create thumbnail from
            size: Thumbnail size as (width, height)
            
        Returns:
            Thumbnail PIL Image
        """
        thumbnail = image.copy()
        thumbnail.thumbnail(size, Image.Resampling.LANCZOS)
        return thumbnail
    
    def save_processed_image(self, image: Image.Image, 
                           output_path: str, 
                           format: str = 'JPEG') -> bool:
        """
        Save processed image to file.
        
        Args:
            image: PIL Image to save
            output_path: Path to save the image
            format: Image format (JPEG, PNG, etc.)
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Ensure directory exists
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # Save with appropriate quality
            if format.upper() == 'JPEG':
                image.save(output_path, format=format, quality=self.quality, optimize=True)
            else:
                image.save(output_path, format=format, optimize=True)
            
            logger.info(f"Image saved to {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error saving image: {str(e)}")
            return False
    
    def get_image_info(self, image_file: InMemoryUploadedFile) -> dict:
        """
        Get detailed information about the image.
        
        Args:
            image_file: Uploaded image file
            
        Returns:
            Dictionary with image information
        """
        try:
            image = Image.open(image_file)
            
            info = {
                'filename': image_file.name,
                'size': image.size,
                'mode': image.mode,
                'format': image.format,
                'file_size': image_file.size,
                'file_size_formatted': self._format_file_size(image_file.size),
                'has_transparency': image.mode in ('RGBA', 'LA') or 'transparency' in image.info,
                'dpi': image.info.get('dpi', (72, 72)),
            }
            
            # Reset file pointer
            image_file.seek(0)
            
            return info
            
        except Exception as e:
            logger.error(f"Error getting image info: {str(e)}")
            return {'error': str(e)}
    
    def _format_file_size(self, size_bytes: int) -> str:
        """
        Format file size in human-readable format.
        
        Args:
            size_bytes: Size in bytes
            
        Returns:
            Formatted size string
        """
        if size_bytes == 0:
            return "0 B"
        
        size_names = ["B", "KB", "MB", "GB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        
        return f"{size_bytes:.1f} {size_names[i]}"
    
    def extract_image_features(self, image: Image.Image) -> dict:
        """
        Extract basic features from the image for analysis.
        
        Args:
            image: PIL Image to analyze
            
        Returns:
            Dictionary with extracted features
        """
        try:
            # Convert to RGB if not already
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # Get basic statistics
            width, height = image.size
            aspect_ratio = width / height
            
            # Calculate average colors
            pixels = list(image.getdata())
            total_pixels = len(pixels)
            
            avg_red = sum(pixel[0] for pixel in pixels) / total_pixels
            avg_green = sum(pixel[1] for pixel in pixels) / total_pixels
            avg_blue = sum(pixel[2] for pixel in pixels) / total_pixels
            
            # Calculate brightness
            brightness = (avg_red + avg_green + avg_blue) / 3
            
            # Calculate green dominance (important for leaf analysis)
            green_dominance = avg_green / (avg_red + avg_green + avg_blue) if (avg_red + avg_green + avg_blue) > 0 else 0
            
            features = {
                'width': width,
                'height': height,
                'aspect_ratio': aspect_ratio,
                'total_pixels': total_pixels,
                'avg_colors': {
                    'red': avg_red,
                    'green': avg_green,
                    'blue': avg_blue
                },
                'brightness': brightness,
                'green_dominance': green_dominance,
                'is_landscape': width > height,
                'is_square': abs(aspect_ratio - 1.0) < 0.1
            }
            
            return features
            
        except Exception as e:
            logger.error(f"Error extracting image features: {str(e)}")
            return {'error': str(e)}

# Convenience functions
def validate_uploaded_image(image_file: InMemoryUploadedFile) -> Tuple[bool, str]:
    """
    Quick function to validate an uploaded image.
    
    Args:
        image_file: Uploaded image file
        
    Returns:
        Tuple of (is_valid, error_message)
    """
    processor = ImageProcessor()
    return processor.validate_image(image_file)

def preprocess_uploaded_image(image_file: InMemoryUploadedFile, 
                            enhance: bool = True) -> Image.Image:
    """
    Quick function to preprocess an uploaded image.
    
    Args:
        image_file: Uploaded image file
        enhance: Whether to apply enhancements
        
    Returns:
        Processed PIL Image
    """
    processor = ImageProcessor()
    return processor.preprocess_image(image_file, enhance)
