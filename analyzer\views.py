from django.shortcuts import render, get_object_or_404, redirect
from django.http import JsonResponse, HttpResponse
from django.contrib import messages
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.views.generic import ListView, DetailView
from django.core.paginator import Paginator
from django.db.models import Q
from django.utils import timezone
import json
import time
import logging

from .models import AnalysisResult, DiseaseInfo
from .forms import ImageUploadForm, AnalysisFilterForm, FeedbackForm
from .utils.gemini import analyze_tomato_leaf
from .utils.image_processing import validate_uploaded_image, preprocess_uploaded_image

# Configure logging
logger = logging.getLogger(__name__)

def index(request):
    """Home page view."""
    # Get recent analysis statistics
    recent_analyses = AnalysisResult.objects.filter(
        created_at__gte=timezone.now() - timezone.timedelta(days=30)
    )

    stats = {
        'total_analyses': AnalysisResult.objects.count(),
        'recent_analyses': recent_analyses.count(),
        'healthy_plants': recent_analyses.filter(is_healthy=True).count(),
        'diseases_detected': recent_analyses.filter(is_healthy=False).count(),
    }

    # Get recent analysis results for display
    recent_results = AnalysisResult.objects.filter(
        error_message__isnull=True
    )[:6]

    context = {
        'stats': stats,
        'recent_results': recent_results,
    }

    return render(request, 'analyzer/index.html', context)

def upload(request):
    """Upload and analysis page."""
    form = ImageUploadForm()

    context = {
        'form': form,
    }

    return render(request, 'analyzer/upload.html', context)

@require_http_methods(["POST"])
def analyze(request):
    """Handle image analysis via HTMX."""
    start_time = time.time()

    try:
        form = ImageUploadForm(request.POST, request.FILES)

        if form.is_valid():
            # Create analysis result instance
            analysis_result = form.save(commit=False)

            # Get form data
            image_file = form.cleaned_data['image']
            analysis_type = form.cleaned_data['analysis_type']
            confidence_threshold = form.cleaned_data['confidence_threshold']

            # Store image metadata
            analysis_result.image_size = image_file.size

            # Validate and preprocess image
            is_valid, error_message = validate_uploaded_image(image_file)
            if not is_valid:
                analysis_result.error_message = error_message
                analysis_result.save()
                return render(request, 'components/analysis_results.html', {
                    'analysis_result': analysis_result
                })

            # Preprocess image to get dimensions
            try:
                processed_image = preprocess_uploaded_image(image_file, enhance=False)
                analysis_result.image_width = processed_image.size[0]
                analysis_result.image_height = processed_image.size[1]
            except Exception as e:
                logger.warning(f"Could not get image dimensions: {str(e)}")

            # Save the analysis result first
            analysis_result.save()

            # Perform AI analysis
            try:
                logger.info(f"Starting analysis for image: {image_file.name}")

                # Reset file pointer
                image_file.seek(0)

                # Analyze with Gemini
                gemini_result = analyze_tomato_leaf(
                    image_file,
                    analysis_type,
                    confidence_threshold
                )

                # Update analysis result with Gemini response
                analysis_result.gemini_response = gemini_result

                if gemini_result.get('error'):
                    analysis_result.error_message = gemini_result.get('error_message', 'Analysis failed')
                else:
                    # Extract results
                    analysis_result.is_healthy = gemini_result.get('is_healthy', None)
                    analysis_result.disease_name = gemini_result.get('disease_detected') or gemini_result.get('disease_name')
                    analysis_result.confidence = gemini_result.get('confidence', 0)
                    analysis_result.severity = gemini_result.get('severity', 'unknown')
                    analysis_result.symptoms_observed = gemini_result.get('symptoms_observed', [])
                    analysis_result.description = gemini_result.get('description', '')
                    analysis_result.recommendations = gemini_result.get('recommendations', '')

                # Calculate analysis duration
                analysis_result.analysis_duration = time.time() - start_time

                # Save updated result
                analysis_result.save()

                logger.info(f"Analysis completed in {analysis_result.analysis_duration:.2f} seconds")

            except Exception as e:
                logger.error(f"Error during analysis: {str(e)}")
                analysis_result.error_message = f"Analysis failed: {str(e)}"
                analysis_result.analysis_duration = time.time() - start_time
                analysis_result.save()

            # Return results via HTMX
            return render(request, 'components/analysis_results.html', {
                'analysis_result': analysis_result
            })

        else:
            # Form validation failed
            logger.warning(f"Form validation failed: {form.errors}")
            return JsonResponse({
                'error': True,
                'message': 'Please correct the form errors and try again.',
                'errors': form.errors
            }, status=400)

    except Exception as e:
        logger.error(f"Unexpected error in analyze view: {str(e)}")
        return JsonResponse({
            'error': True,
            'message': 'An unexpected error occurred. Please try again.'
        }, status=500)

def results(request, analysis_id=None):
    """Display analysis results."""
    analysis_result = None

    if analysis_id:
        analysis_result = get_object_or_404(AnalysisResult, id=analysis_id)

    context = {
        'analysis_result': analysis_result,
    }

    return render(request, 'analyzer/results.html', context)
