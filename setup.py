#!/usr/bin/env python
"""
TomatoGuard Setup Script
Automates the initial setup process for the TomatoGuard application.
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def print_header():
    """Print the setup header."""
    print("🍅" * 20)
    print("   TomatoGuard Setup Script")
    print("   AI-Powered Tomato Disease Detection")
    print("🍅" * 20)
    print()

def check_python_version():
    """Check if Python version is compatible."""
    print("Checking Python version...")
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        print(f"   Current version: {sys.version}")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} detected")
    return True

def check_pip():
    """Check if pip is available."""
    print("Checking pip...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "--version"], 
                      check=True, capture_output=True)
        print("✅ pip is available")
        return True
    except subprocess.CalledProcessError:
        print("❌ pip is not available")
        return False

def install_requirements():
    """Install Python requirements."""
    print("Installing Python requirements...")
    try:
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                      check=True)
        print("✅ Requirements installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install requirements: {e}")
        return False

def setup_environment():
    """Set up environment file."""
    print("Setting up environment file...")
    
    env_example = Path(".env.example")
    env_file = Path(".env")
    
    if not env_example.exists():
        print("❌ .env.example file not found")
        return False
    
    if env_file.exists():
        print("⚠️  .env file already exists, skipping...")
        return True
    
    try:
        shutil.copy(env_example, env_file)
        print("✅ .env file created from template")
        print("   Please edit .env file and add your Gemini API key")
        return True
    except Exception as e:
        print(f"❌ Failed to create .env file: {e}")
        return False

def run_migrations():
    """Run Django migrations."""
    print("Running Django migrations...")
    try:
        subprocess.run([sys.executable, "manage.py", "makemigrations"], check=True)
        subprocess.run([sys.executable, "manage.py", "migrate"], check=True)
        print("✅ Migrations completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Migration failed: {e}")
        return False

def create_superuser():
    """Prompt to create superuser."""
    print("Creating Django superuser...")
    response = input("Would you like to create a superuser account? (y/n): ").lower()
    
    if response in ['y', 'yes']:
        try:
            subprocess.run([sys.executable, "manage.py", "createsuperuser"], check=True)
            print("✅ Superuser created successfully")
            return True
        except subprocess.CalledProcessError:
            print("⚠️  Superuser creation skipped or failed")
            return True
    else:
        print("⚠️  Superuser creation skipped")
        return True

def test_setup():
    """Test the setup."""
    print("Testing setup...")
    try:
        # Test basic imports
        subprocess.run([sys.executable, "-c", 
                       "import django; django.setup(); from analyzer.models import AnalysisResult"], 
                      check=True, capture_output=True)
        print("✅ Basic setup test passed")
        
        # Test Gemini utilities (without API call)
        subprocess.run([sys.executable, "manage.py", "test_gemini", "--skip-api"], 
                      check=True, capture_output=True)
        print("✅ Gemini integration test passed")
        return True
    except subprocess.CalledProcessError as e:
        print(f"⚠️  Setup test failed: {e}")
        print("   This might be due to missing Gemini API key")
        return True  # Don't fail setup for this

def print_next_steps():
    """Print next steps for the user."""
    print("\n" + "🎉" * 20)
    print("   Setup Complete!")
    print("🎉" * 20)
    print("\nNext Steps:")
    print("1. Edit the .env file and add your Gemini API key:")
    print("   GEMINI_API_KEY=your-api-key-here")
    print("   Get your key from: https://makersuite.google.com/app/apikey")
    print()
    print("2. Test the Gemini integration:")
    print("   python manage.py test_gemini")
    print()
    print("3. Start the development server:")
    print("   python manage.py runserver")
    print()
    print("4. Open your browser and visit:")
    print("   http://127.0.0.1:8000/")
    print()
    print("5. Admin panel (if you created a superuser):")
    print("   http://127.0.0.1:8000/admin/")
    print()
    print("📚 Documentation: See README.md for detailed information")
    print("🐛 Issues: Report bugs via GitHub Issues")
    print()

def main():
    """Main setup function."""
    print_header()
    
    # Check prerequisites
    if not check_python_version():
        return False
    
    if not check_pip():
        return False
    
    # Setup steps
    steps = [
        install_requirements,
        setup_environment,
        run_migrations,
        create_superuser,
        test_setup,
    ]
    
    for step in steps:
        if not step():
            print(f"\n❌ Setup failed at step: {step.__name__}")
            print("Please check the error messages above and try again.")
            return False
        print()
    
    print_next_steps()
    return True

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⚠️  Setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n❌ Unexpected error during setup: {e}")
        sys.exit(1)
