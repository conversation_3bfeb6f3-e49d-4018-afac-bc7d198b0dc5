/* Custom CSS for TomatoGuard */

/* HTMX Loading Indicators */
.htmx-indicator {
    display: none;
}

.htmx-request .htmx-indicator {
    display: flex;
}

.htmx-request.htmx-indicator {
    display: flex;
}

/* Custom Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slideDown {
    from {
        transform: translateY(-20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -30px, 0);
    }
    70% {
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0, -4px, 0);
    }
}

/* Animation Classes */
.animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
    animation: slideUp 0.6s ease-out;
}

.animate-slide-down {
    animation: slideDown 0.6s ease-out;
}

.animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-bounce-slow {
    animation: bounce 2s infinite;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #dc2626;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #b91c1c;
}

/* File Upload Drag and Drop Styles */
.drag-over {
    border-color: #dc2626 !important;
    background-color: #fef2f2 !important;
}

.file-preview {
    transition: all 0.3s ease;
}

.file-preview:hover {
    transform: scale(1.02);
}

/* Custom Button Styles */
.btn-primary {
    background: linear-gradient(135deg, #dc2626, #ef4444);
    border: none;
    color: white;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.2s ease;
    box-shadow: 0 4px 6px rgba(220, 38, 38, 0.2);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #b91c1c, #dc2626);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(220, 38, 38, 0.3);
}

.btn-primary:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(220, 38, 38, 0.2);
}

.btn-secondary {
    background: linear-gradient(135deg, #6b7280, #9ca3af);
    border: none;
    color: white;
    padding: 12px 24px;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.2s ease;
    box-shadow: 0 4px 6px rgba(107, 114, 128, 0.2);
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #4b5563, #6b7280);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(107, 114, 128, 0.3);
}

/* Card Hover Effects */
.card-hover {
    transition: all 0.3s ease;
}

.card-hover:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Progress Bar Animations */
.progress-bar {
    transition: width 0.5s ease-in-out;
}

/* Image Gallery Styles */
.image-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.image-gallery-item {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.image-gallery-item:hover {
    transform: scale(1.05);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.image-gallery-item img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.image-gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.image-gallery-item:hover .image-gallery-overlay {
    opacity: 1;
}

/* Loading Spinner */
.spinner {
    border: 4px solid #f3f3f3;
    border-top: 4px solid #dc2626;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    max-width: 400px;
    padding: 16px;
    border-radius: 8px;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.notification.success {
    background-color: #10b981;
    color: white;
}

.notification.error {
    background-color: #ef4444;
    color: white;
}

.notification.warning {
    background-color: #f59e0b;
    color: white;
}

.notification.info {
    background-color: #3b82f6;
    color: white;
}

/* Modal Styles */
.modal-backdrop {
    backdrop-filter: blur(4px);
}

.modal-content {
    max-height: 90vh;
    overflow-y: auto;
}

/* Responsive Design Helpers */
@media (max-width: 768px) {
    .mobile-hidden {
        display: none;
    }
    
    .mobile-full {
        width: 100%;
    }
    
    .mobile-text-center {
        text-align: center;
    }
}

/* Print Styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    .print-break {
        page-break-before: always;
    }
    
    body {
        background: white !important;
        color: black !important;
    }
    
    .bg-gradient-to-r,
    .bg-gradient-to-br,
    .bg-gradient-to-l {
        background: white !important;
    }
}

/* Accessibility Improvements */
.focus-visible:focus {
    outline: 2px solid #dc2626;
    outline-offset: 2px;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .btn-primary {
        background: #000000;
        border: 2px solid #ffffff;
    }
    
    .btn-secondary {
        background: #ffffff;
        color: #000000;
        border: 2px solid #000000;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
