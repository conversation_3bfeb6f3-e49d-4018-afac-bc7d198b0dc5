// TomatoGuard Main JavaScript

// Global App Object
window.TomatoGuard = {
    // Configuration
    config: {
        maxFileSize: 5 * 1024 * 1024, // 5MB
        allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/bmp', 'image/tiff'],
        apiEndpoints: {
            analyze: '/analyzer/analyze/',
            upload: '/analyzer/upload/'
        }
    },
    
    // Utility Functions
    utils: {
        // Format file size
        formatFileSize: function(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        },
        
        // Validate image file
        validateImageFile: function(file) {
            const errors = [];
            
            // Check file type
            if (!this.config.allowedTypes.includes(file.type)) {
                errors.push('Please select a valid image file (JPG, PNG, BMP, TIFF)');
            }
            
            // Check file size
            if (file.size > this.config.maxFileSize) {
                errors.push('File size must be less than 5MB');
            }
            
            return {
                isValid: errors.length === 0,
                errors: errors
            };
        }.bind(window.TomatoGuard),
        
        // Show notification
        showNotification: function(message, type = 'info', duration = 5000) {
            const notification = document.createElement('div');
            notification.className = `notification ${type} animate-slide-down`;
            notification.innerHTML = `
                <div class="flex items-center justify-between">
                    <span>${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            `;
            
            document.body.appendChild(notification);
            
            // Auto remove after duration
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.style.transform = 'translateX(100%)';
                    notification.style.opacity = '0';
                    setTimeout(() => notification.remove(), 300);
                }
            }, duration);
        },
        
        // Copy to clipboard
        copyToClipboard: function(text) {
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(() => {
                    this.showNotification('Copied to clipboard!', 'success');
                });
            } else {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                this.showNotification('Copied to clipboard!', 'success');
            }
        }.bind(window.TomatoGuard.utils),
        
        // Debounce function
        debounce: function(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
    },
    
    // Image handling
    image: {
        // Create image preview
        createPreview: function(file, callback) {
            const reader = new FileReader();
            reader.onload = function(e) {
                callback(e.target.result);
            };
            reader.readAsDataURL(file);
        },
        
        // Compress image if needed
        compressImage: function(file, maxWidth = 1024, quality = 0.8) {
            return new Promise((resolve) => {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const img = new Image();
                
                img.onload = function() {
                    // Calculate new dimensions
                    let { width, height } = img;
                    if (width > maxWidth) {
                        height = (height * maxWidth) / width;
                        width = maxWidth;
                    }
                    
                    canvas.width = width;
                    canvas.height = height;
                    
                    // Draw and compress
                    ctx.drawImage(img, 0, 0, width, height);
                    canvas.toBlob(resolve, 'image/jpeg', quality);
                };
                
                img.src = URL.createObjectURL(file);
            });
        }
    },
    
    // Analysis functions
    analysis: {
        // Submit analysis request
        submitAnalysis: function(formData, onProgress, onSuccess, onError) {
            const xhr = new XMLHttpRequest();
            
            // Progress tracking
            if (onProgress) {
                xhr.upload.addEventListener('progress', (e) => {
                    if (e.lengthComputable) {
                        const percentComplete = (e.loaded / e.total) * 100;
                        onProgress(percentComplete);
                    }
                });
            }
            
            // Success handler
            xhr.addEventListener('load', () => {
                if (xhr.status === 200) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        onSuccess(response);
                    } catch (e) {
                        onError('Invalid response from server');
                    }
                } else {
                    onError(`Server error: ${xhr.status}`);
                }
            });
            
            // Error handler
            xhr.addEventListener('error', () => {
                onError('Network error occurred');
            });
            
            // Send request
            xhr.open('POST', window.TomatoGuard.config.apiEndpoints.analyze);
            xhr.setRequestHeader('X-CSRFToken', this.getCSRFToken());
            xhr.send(formData);
        },
        
        // Get CSRF token
        getCSRFToken: function() {
            const cookies = document.cookie.split(';');
            for (let cookie of cookies) {
                const [name, value] = cookie.trim().split('=');
                if (name === 'csrftoken') {
                    return value;
                }
            }
            return '';
        }
    },
    
    // UI interactions
    ui: {
        // Initialize tooltips
        initTooltips: function() {
            const tooltips = document.querySelectorAll('[data-tooltip]');
            tooltips.forEach(element => {
                element.addEventListener('mouseenter', this.showTooltip);
                element.addEventListener('mouseleave', this.hideTooltip);
            });
        },
        
        // Show tooltip
        showTooltip: function(e) {
            const tooltip = document.createElement('div');
            tooltip.className = 'tooltip';
            tooltip.textContent = e.target.dataset.tooltip;
            tooltip.style.cssText = `
                position: absolute;
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 8px 12px;
                border-radius: 4px;
                font-size: 14px;
                z-index: 1000;
                pointer-events: none;
                white-space: nowrap;
            `;
            
            document.body.appendChild(tooltip);
            
            const rect = e.target.getBoundingClientRect();
            tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
            tooltip.style.top = rect.top - tooltip.offsetHeight - 8 + 'px';
            
            e.target._tooltip = tooltip;
        },
        
        // Hide tooltip
        hideTooltip: function(e) {
            if (e.target._tooltip) {
                e.target._tooltip.remove();
                delete e.target._tooltip;
            }
        },
        
        // Smooth scroll to element
        scrollToElement: function(selector, offset = 0) {
            const element = document.querySelector(selector);
            if (element) {
                const elementPosition = element.getBoundingClientRect().top + window.pageYOffset;
                window.scrollTo({
                    top: elementPosition - offset,
                    behavior: 'smooth'
                });
            }
        }
    },
    
    // Initialize the app
    init: function() {
        console.log('TomatoGuard App Initialized');
        
        // Initialize UI components
        this.ui.initTooltips();
        
        // Add global event listeners
        this.addGlobalEventListeners();
        
        // Initialize HTMX event handlers
        this.initHTMXHandlers();
    },
    
    // Add global event listeners
    addGlobalEventListeners: function() {
        // Handle escape key for modals
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                // Close any open modals
                const modals = document.querySelectorAll('[x-data*="Modal"]');
                modals.forEach(modal => {
                    if (modal._x_dataStack) {
                        const data = modal._x_dataStack[0];
                        Object.keys(data).forEach(key => {
                            if (key.includes('Modal') || key.includes('show')) {
                                data[key] = false;
                            }
                        });
                    }
                });
            }
        });
        
        // Handle image drag and drop globally
        document.addEventListener('dragover', (e) => {
            e.preventDefault();
        });
        
        document.addEventListener('drop', (e) => {
            e.preventDefault();
        });
    },
    
    // Initialize HTMX event handlers
    initHTMXHandlers: function() {
        // Before request
        document.addEventListener('htmx:beforeRequest', (e) => {
            console.log('HTMX request starting:', e.detail.requestConfig.path);
        });
        
        // After request
        document.addEventListener('htmx:afterRequest', (e) => {
            console.log('HTMX request completed:', e.detail.xhr.status);
            
            if (e.detail.xhr.status >= 400) {
                this.utils.showNotification('Request failed. Please try again.', 'error');
            }
        });
        
        // Response error
        document.addEventListener('htmx:responseError', (e) => {
            console.error('HTMX response error:', e.detail);
            this.utils.showNotification('Server error occurred. Please try again.', 'error');
        });
        
        // Network error
        document.addEventListener('htmx:sendError', (e) => {
            console.error('HTMX network error:', e.detail);
            this.utils.showNotification('Network error. Please check your connection.', 'error');
        });
    }
};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.TomatoGuard.init();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = window.TomatoGuard;
}
