{% extends 'base.html' %}
{% load static %}

{% block title %}TomatoGuard - AI-Powered Tomato Disease Detection{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="relative bg-gradient-to-br from-leaf-500 via-tomato-500 to-leaf-600 text-white py-20 overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-10">
        <svg class="w-full h-full" viewBox="0 0 100 100" xmlns="http://www.w3.org/2000/svg">
            <defs>
                <pattern id="leaf-pattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse">
                    <path d="M10 2C6 2 2 6 2 10s4 8 8 8 8-4 8-8-4-8-8-8z" fill="currentColor"/>
                </pattern>
            </defs>
            <rect width="100" height="100" fill="url(#leaf-pattern)"/>
        </svg>
    </div>
    
    <div class="container mx-auto px-4 relative z-10">
        <div class="max-w-4xl mx-auto text-center">
            <h1 class="text-5xl md:text-6xl font-bold mb-6 animate-fade-in">
                Protect Your <span class="text-yellow-300">Tomato</span> Crops
            </h1>
            <p class="text-xl md:text-2xl mb-8 text-white text-opacity-90 animate-slide-up">
                Advanced AI-powered disease detection for healthier plants and better harvests
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center items-center animate-slide-up">
                <a href="{% url 'analyzer:upload' %}" 
                   class="bg-white text-tomato-600 px-8 py-4 rounded-lg font-semibold text-lg 
                          hover:bg-gray-100 transition-all duration-200 transform hover:scale-105 
                          shadow-lg hover:shadow-xl flex items-center space-x-2">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                    </svg>
                    <span>Start Analysis</span>
                </a>
                <button onclick="scrollToFeatures()" 
                        class="border-2 border-white text-white px-8 py-4 rounded-lg font-semibold text-lg 
                               hover:bg-white hover:text-tomato-600 transition-all duration-200 
                               flex items-center space-x-2">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span>Learn More</span>
                </button>
            </div>
        </div>
    </div>
    
    <!-- Floating Elements -->
    <div class="absolute top-20 left-10 w-20 h-20 bg-white bg-opacity-20 rounded-full animate-pulse-slow"></div>
    <div class="absolute bottom-20 right-10 w-16 h-16 bg-yellow-300 bg-opacity-30 rounded-full animate-pulse-slow" style="animation-delay: 1s;"></div>
    <div class="absolute top-1/2 left-1/4 w-12 h-12 bg-green-300 bg-opacity-25 rounded-full animate-pulse-slow" style="animation-delay: 2s;"></div>
</section>

<!-- Features Section -->
<section id="features" class="py-20 bg-white">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16">
            <h2 class="text-4xl font-bold text-gray-800 mb-4">Why Choose TomatoGuard?</h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Our advanced AI technology provides accurate, fast, and reliable disease detection 
                to help you maintain healthy tomato plants.
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <!-- Feature 1 -->
            <div class="text-center p-6 rounded-xl bg-gradient-to-br from-blue-50 to-blue-100 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-2">
                <div class="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-800 mb-3">Lightning Fast</h3>
                <p class="text-gray-600">Get results in seconds with our optimized AI models powered by Google Gemini.</p>
            </div>
            
            <!-- Feature 2 -->
            <div class="text-center p-6 rounded-xl bg-gradient-to-br from-green-50 to-green-100 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-2">
                <div class="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-800 mb-3">Highly Accurate</h3>
                <p class="text-gray-600">Advanced machine learning ensures precise disease identification with confidence scores.</p>
            </div>
            
            <!-- Feature 3 -->
            <div class="text-center p-6 rounded-xl bg-gradient-to-br from-purple-50 to-purple-100 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-2">
                <div class="w-16 h-16 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                              d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-800 mb-3">Expert Guidance</h3>
                <p class="text-gray-600">Receive detailed recommendations and treatment suggestions for detected diseases.</p>
            </div>
        </div>
    </div>
</section>

<!-- How It Works Section -->
<section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
        <div class="text-center mb-16">
            <h2 class="text-4xl font-bold text-gray-800 mb-4">How It Works</h2>
            <p class="text-xl text-gray-600">Simple, fast, and effective disease detection in three easy steps</p>
        </div>
        
        <div class="max-w-4xl mx-auto">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <!-- Step 1 -->
                <div class="text-center">
                    <div class="relative mb-6">
                        <div class="w-20 h-20 bg-tomato-500 rounded-full flex items-center justify-center mx-auto">
                            <span class="text-2xl font-bold text-white">1</span>
                        </div>
                        <div class="hidden md:block absolute top-10 left-full w-full h-0.5 bg-gray-300 transform -translate-y-1/2"></div>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-800 mb-3">Upload Image</h3>
                    <p class="text-gray-600">Take a clear photo of your tomato leaf and upload it to our platform.</p>
                </div>
                
                <!-- Step 2 -->
                <div class="text-center">
                    <div class="relative mb-6">
                        <div class="w-20 h-20 bg-tomato-500 rounded-full flex items-center justify-center mx-auto">
                            <span class="text-2xl font-bold text-white">2</span>
                        </div>
                        <div class="hidden md:block absolute top-10 left-full w-full h-0.5 bg-gray-300 transform -translate-y-1/2"></div>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-800 mb-3">AI Analysis</h3>
                    <p class="text-gray-600">Our advanced AI analyzes the image for signs of diseases and health issues.</p>
                </div>
                
                <!-- Step 3 -->
                <div class="text-center">
                    <div class="relative mb-6">
                        <div class="w-20 h-20 bg-tomato-500 rounded-full flex items-center justify-center mx-auto">
                            <span class="text-2xl font-bold text-white">3</span>
                        </div>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-800 mb-3">Get Results</h3>
                    <p class="text-gray-600">Receive detailed analysis results with treatment recommendations.</p>
                </div>
            </div>
        </div>
        
        <div class="text-center mt-12">
            <a href="{% url 'analyzer:upload' %}" 
               class="bg-gradient-to-r from-tomato-500 to-tomato-600 text-white px-8 py-4 rounded-lg 
                      font-semibold text-lg hover:from-tomato-600 hover:to-tomato-700 
                      transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl 
                      inline-flex items-center space-x-2">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                          d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                </svg>
                <span>Try It Now</span>
            </a>
        </div>
    </div>
</section>

<!-- Statistics Section -->
<section class="py-20 bg-gradient-to-r from-tomato-600 to-leaf-600 text-white">
    <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8 text-center">
            <div>
                <div class="text-4xl font-bold mb-2" x-data="{ count: 0 }" x-init="setInterval(() => { if(count < 10000) count += 100 }, 50)">
                    <span x-text="count.toLocaleString()">0</span>+
                </div>
                <p class="text-white text-opacity-90">Images Analyzed</p>
            </div>
            <div>
                <div class="text-4xl font-bold mb-2">95%</div>
                <p class="text-white text-opacity-90">Accuracy Rate</p>
            </div>
            <div>
                <div class="text-4xl font-bold mb-2">24/7</div>
                <p class="text-white text-opacity-90">Available</p>
            </div>
            <div>
                <div class="text-4xl font-bold mb-2">15+</div>
                <p class="text-white text-opacity-90">Disease Types</p>
            </div>
        </div>
    </div>
</section>

<script>
function scrollToFeatures() {
    document.getElementById('features').scrollIntoView({ 
        behavior: 'smooth' 
    });
}
</script>
{% endblock %}
