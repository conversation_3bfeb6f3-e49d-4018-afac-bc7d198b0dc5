{% extends 'base.html' %}
{% load static %}

{% block title %}Upload Image - TomatoGuard{% endblock %}

{% block extra_head %}
<style>
    .slider::-webkit-slider-thumb {
        appearance: none;
        height: 20px;
        width: 20px;
        border-radius: 50%;
        background: #dc2626;
        cursor: pointer;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }

    .slider::-moz-range-thumb {
        height: 20px;
        width: 20px;
        border-radius: 50%;
        background: #dc2626;
        cursor: pointer;
        border: none;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }
</style>
{% endblock %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-leaf-50 to-tomato-50 py-12">
    <div class="container mx-auto px-4">
        <!-- Header -->
        <div class="text-center mb-12">
            <h1 class="text-4xl md:text-5xl font-bold text-gray-800 mb-4">
                Analyze Your Tomato Leaves
            </h1>
            <p class="text-xl text-gray-600 max-w-2xl mx-auto">
                Upload a clear image of your tomato leaf and let our AI detect any diseases or health issues
            </p>
        </div>

        <!-- Main Content -->
        <div class="max-w-4xl mx-auto">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Upload Form -->
                <div class="lg:col-span-2">
                    {% include 'components/upload_form.html' %}
                </div>

                <!-- Tips and Guidelines -->
                <div class="space-y-6">
                    <!-- Photo Tips -->
                    <div class="bg-white rounded-xl shadow-lg p-6">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center space-x-2">
                            <svg class="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            <span>Photo Tips</span>
                        </h3>
                        <ul class="space-y-3 text-sm text-gray-600">
                            <li class="flex items-start space-x-2">
                                <svg class="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Use good lighting (natural light preferred)</span>
                            </li>
                            <li class="flex items-start space-x-2">
                                <svg class="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Keep the leaf in focus and centered</span>
                            </li>
                            <li class="flex items-start space-x-2">
                                <svg class="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Avoid shadows and reflections</span>
                            </li>
                            <li class="flex items-start space-x-2">
                                <svg class="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Include the entire leaf if possible</span>
                            </li>
                            <li class="flex items-start space-x-2">
                                <svg class="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span>Use high resolution (at least 1MP)</span>
                            </li>
                        </ul>
                    </div>

                    <!-- Supported Diseases -->
                    <div class="bg-white rounded-xl shadow-lg p-6">
                        <h3 class="text-lg font-semibold text-gray-800 mb-4 flex items-center space-x-2">
                            <svg class="w-5 h-5 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                      d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                            <span>Detectable Diseases</span>
                        </h3>
                        <div class="space-y-2 text-sm text-gray-600">
                            <div class="flex items-center space-x-2">
                                <div class="w-2 h-2 bg-red-400 rounded-full"></div>
                                <span>Early Blight</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <div class="w-2 h-2 bg-red-400 rounded-full"></div>
                                <span>Late Blight</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <div class="w-2 h-2 bg-red-400 rounded-full"></div>
                                <span>Leaf Mold</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <div class="w-2 h-2 bg-red-400 rounded-full"></div>
                                <span>Septoria Leaf Spot</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <div class="w-2 h-2 bg-red-400 rounded-full"></div>
                                <span>Bacterial Spot</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <div class="w-2 h-2 bg-red-400 rounded-full"></div>
                                <span>Target Spot</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <div class="w-2 h-2 bg-red-400 rounded-full"></div>
                                <span>Mosaic Virus</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <div class="w-2 h-2 bg-red-400 rounded-full"></div>
                                <span>Yellow Leaf Curl Virus</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <div class="w-2 h-2 bg-green-400 rounded-full"></div>
                                <span>Healthy Leaves</span>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Stats -->
                    <div class="bg-gradient-to-br from-tomato-500 to-leaf-500 rounded-xl shadow-lg p-6 text-white">
                        <h3 class="text-lg font-semibold mb-4">Analysis Stats</h3>
                        <div class="space-y-3">
                            <div class="flex justify-between items-center">
                                <span class="text-white text-opacity-90">Average Analysis Time</span>
                                <span class="font-semibold">3-5 seconds</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-white text-opacity-90">Accuracy Rate</span>
                                <span class="font-semibold">95%+</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-white text-opacity-90">Supported Formats</span>
                                <span class="font-semibold">JPG, PNG, BMP, TIFF</span>
                            </div>
                            <div class="flex justify-between items-center">
                                <span class="text-white text-opacity-90">Max File Size</span>
                                <span class="font-semibold">5MB</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Analysis Results Container -->
            <div id="analysis-results" class="mt-8">
                <!-- Results will be loaded here via HTMX -->
            </div>
        </div>

        <!-- Sample Images Section -->
        <div class="mt-16">
            <div class="text-center mb-8">
                <h2 class="text-3xl font-bold text-gray-800 mb-4">Sample Images</h2>
                <p class="text-gray-600">See examples of good quality images for analysis</p>
            </div>

            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-4xl mx-auto">
                <!-- Sample Image Placeholders -->
                <div class="relative group">
                    <div class="w-full h-32 bg-gradient-to-br from-green-100 to-green-200 rounded-lg shadow-md flex items-center justify-center">
                        <div class="text-center">
                            <svg class="w-8 h-8 text-green-600 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <p class="text-xs text-green-700 font-medium">Healthy Leaf</p>
                        </div>
                    </div>
                    <p class="text-center text-sm text-gray-600 mt-2">Healthy Tomato Leaf</p>
                </div>

                <div class="relative group">
                    <div class="w-full h-32 bg-gradient-to-br from-red-100 to-red-200 rounded-lg shadow-md flex items-center justify-center">
                        <div class="text-center">
                            <svg class="w-8 h-8 text-red-600 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                            <p class="text-xs text-red-700 font-medium">Early Blight</p>
                        </div>
                    </div>
                    <p class="text-center text-sm text-gray-600 mt-2">Early Blight Disease</p>
                </div>

                <div class="relative group">
                    <div class="w-full h-32 bg-gradient-to-br from-orange-100 to-orange-200 rounded-lg shadow-md flex items-center justify-center">
                        <div class="text-center">
                            <svg class="w-8 h-8 text-orange-600 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                            <p class="text-xs text-orange-700 font-medium">Late Blight</p>
                        </div>
                    </div>
                    <p class="text-center text-sm text-gray-600 mt-2">Late Blight Disease</p>
                </div>

                <div class="relative group">
                    <div class="w-full h-32 bg-gradient-to-br from-yellow-100 to-yellow-200 rounded-lg shadow-md flex items-center justify-center">
                        <div class="text-center">
                            <svg class="w-8 h-8 text-yellow-600 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                            <p class="text-xs text-yellow-700 font-medium">Leaf Mold</p>
                        </div>
                    </div>
                    <p class="text-center text-sm text-gray-600 mt-2">Leaf Mold Disease</p>
                </div>
            </div>

            <!-- Upload Instructions -->
            <div class="mt-8 text-center">
                <p class="text-gray-600 text-sm">
                    💡 <strong>Tip:</strong> For best results, upload clear, well-lit images of individual tomato leaves
                </p>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
// HTMX event handlers for upload functionality
document.addEventListener('htmx:beforeRequest', function(event) {
    if (event.detail.requestConfig.path.includes('analyze')) {
        // Show loading state
        const submitBtn = document.querySelector('button[type="submit"]');
        if (submitBtn) {
            submitBtn.disabled = true;
        }
    }
});

document.addEventListener('htmx:afterRequest', function(event) {
    if (event.detail.requestConfig.path.includes('analyze')) {
        // Hide loading state
        const submitBtn = document.querySelector('button[type="submit"]');
        if (submitBtn) {
            submitBtn.disabled = false;
        }

        // Scroll to results if successful
        if (event.detail.xhr.status === 200) {
            setTimeout(() => {
                const resultsElement = document.getElementById('analysis-results');
                if (resultsElement && resultsElement.innerHTML.trim()) {
                    resultsElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            }, 100);
        }
    }
});

document.addEventListener('htmx:responseError', function(event) {
    if (event.detail.requestConfig.path.includes('analyze')) {
        // Show error message
        const errorMsg = 'Analysis failed. Please try again with a different image.';
        if (window.TomatoGuard && window.TomatoGuard.utils) {
            window.TomatoGuard.utils.showNotification(errorMsg, 'error');
        } else {
            alert(errorMsg);
        }
    }
});

// File validation helper
function validateImageFile(file) {
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/bmp', 'image/tiff'];
    const maxSize = 5 * 1024 * 1024; // 5MB

    if (!allowedTypes.includes(file.type)) {
        return { valid: false, message: 'Please select a valid image file (JPG, PNG, BMP, TIFF)' };
    }

    if (file.size > maxSize) {
        return { valid: false, message: 'File size must be less than 5MB' };
    }

    return { valid: true };
}

// Enhanced drag and drop functionality
document.addEventListener('DOMContentLoaded', function() {
    // Prevent default drag behaviors on the entire document
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        document.addEventListener(eventName, preventDefaults, false);
    });

    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    // Add visual feedback for drag over entire page
    let dragCounter = 0;

    document.addEventListener('dragenter', function(e) {
        dragCounter++;
        const uploadArea = document.querySelector('.upload-area');
        if (uploadArea) {
            uploadArea.classList.add('border-tomato-500', 'bg-tomato-50');
        }
    });

    document.addEventListener('dragleave', function(e) {
        dragCounter--;
        if (dragCounter === 0) {
            const uploadArea = document.querySelector('.upload-area');
            if (uploadArea) {
                uploadArea.classList.remove('border-tomato-500', 'bg-tomato-50');
            }
        }
    });

    document.addEventListener('drop', function(e) {
        dragCounter = 0;
        const uploadArea = document.querySelector('.upload-area');
        if (uploadArea) {
            uploadArea.classList.remove('border-tomato-500', 'bg-tomato-50');
        }
    });

    // Debug click functionality
    const uploadArea = document.querySelector('.upload-area');
    const fileInput = document.getElementById('image-input');

    if (uploadArea && fileInput) {
        console.log('Upload area and file input found');

        // Add click event listener as backup
        uploadArea.addEventListener('click', function(e) {
            console.log('Upload area clicked');
            e.preventDefault();
            e.stopPropagation();
            fileInput.click();
        });

        // Debug file input change
        fileInput.addEventListener('change', function(e) {
            console.log('File input changed:', e.target.files);
        });
    } else {
        console.error('Upload area or file input not found');
    }
});
</script>
{% endblock %}
