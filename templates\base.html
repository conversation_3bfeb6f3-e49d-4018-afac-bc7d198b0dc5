<!DOCTYPE html>
<html lang="en" class="h-full">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}TomatoGuard - AI-Powered Leaf Disease Detection{% endblock %}</title>
    
    <!-- Tailwind CSS CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- HTMX -->
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    
    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    
    <!-- Three.js (Optional) -->
    <script src="https://unpkg.com/three@0.160.0/build/three.min.js"></script>
    
    <!-- Custom CSS -->
    {% load static %}
    <link rel="stylesheet" href="{% static 'css/custom.css' %}">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{% static 'images/favicon.ico' %}">
    
    <!-- Custom Tailwind Configuration -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'tomato': {
                            50: '#fef2f2',
                            100: '#fee2e2',
                            200: '#fecaca',
                            300: '#fca5a5',
                            400: '#f87171',
                            500: '#ef4444',
                            600: '#dc2626',
                            700: '#b91c1c',
                            800: '#991b1b',
                            900: '#7f1d1d',
                        },
                        'leaf': {
                            50: '#f0fdf4',
                            100: '#dcfce7',
                            200: '#bbf7d0',
                            300: '#86efac',
                            400: '#4ade80',
                            500: '#22c55e',
                            600: '#16a34a',
                            700: '#15803d',
                            800: '#166534',
                            900: '#14532d',
                        }
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-up': 'slideUp 0.3s ease-out',
                        'pulse-slow': 'pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' },
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(10px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' },
                        }
                    }
                }
            }
        }
    </script>
    
    {% block extra_head %}{% endblock %}
</head>
<body class="h-full bg-gradient-to-br from-leaf-50 to-tomato-50" x-data="{ loading: false }">
    <!-- Loading Overlay -->
    <div x-show="loading" 
         x-transition:enter="transition ease-out duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition ease-in duration-200"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-6 flex items-center space-x-3">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-tomato-600"></div>
            <span class="text-gray-700 font-medium">Analyzing image...</span>
        </div>
    </div>

    <!-- Navigation -->
    {% include 'components/navbar.html' %}
    
    <!-- Main Content -->
    <main class="flex-1">
        <!-- Messages -->
        {% if messages %}
            <div class="container mx-auto px-4 py-4">
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} mb-4 p-4 rounded-lg border-l-4 
                               {% if message.tags == 'error' %}bg-red-50 border-red-500 text-red-700
                               {% elif message.tags == 'warning' %}bg-yellow-50 border-yellow-500 text-yellow-700
                               {% elif message.tags == 'success' %}bg-green-50 border-green-500 text-green-700
                               {% else %}bg-blue-50 border-blue-500 text-blue-700{% endif %}"
                         x-data="{ show: true }" 
                         x-show="show"
                         x-transition:enter="transition ease-out duration-300"
                         x-transition:enter-start="opacity-0 transform scale-90"
                         x-transition:enter-end="opacity-100 transform scale-100">
                        <div class="flex justify-between items-center">
                            <span>{{ message }}</span>
                            <button @click="show = false" class="ml-4 text-gray-400 hover:text-gray-600">
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
        
        {% block content %}{% endblock %}
    </main>
    
    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8 mt-12">
        <div class="container mx-auto px-4 text-center">
            <p>&copy; 2024 TomatoGuard. AI-Powered Plant Disease Detection.</p>
            <p class="text-gray-400 text-sm mt-2">Powered by Google Gemini AI</p>
        </div>
    </footer>
    
    <!-- Modals -->
    {% include 'components/modals.html' %}
    
    <!-- Custom JavaScript -->
    <script src="{% static 'js/app.js' %}"></script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>
