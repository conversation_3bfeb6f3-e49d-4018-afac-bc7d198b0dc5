<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upload Test - TomatoGuard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <style>
        .slider::-webkit-slider-thumb {
            appearance: none;
            height: 20px;
            width: 20px;
            border-radius: 50%;
            background: #dc2626;
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        
        .slider::-moz-range-thumb {
            height: 20px;
            width: 20px;
            border-radius: 50%;
            background: #dc2626;
            cursor: pointer;
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-2xl mx-auto">
        <h1 class="text-3xl font-bold text-gray-800 mb-8 text-center">Upload Functionality Test</h1>
        
        <!-- Upload Form Component Test -->
        <div class="bg-white rounded-xl shadow-lg p-8" 
             x-data="uploadForm()" 
             x-init="init()">
            
            <form class="space-y-6" id="upload-form" @submit.prevent="handleSubmit">
                
                <!-- Upload Area -->
                <div class="relative">
                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center transition-all duration-200 upload-area cursor-pointer"
                         :class="{ 'border-red-500 bg-red-50': isDragOver, 'border-gray-300': !isDragOver }"
                         @dragenter.prevent="isDragOver = true"
                         @dragover.prevent="isDragOver = true"
                         @dragleave.prevent="handleDragLeave($event)"
                         @drop.prevent="handleDrop($event)"
                         @click="$refs.fileInput.click()">
                        
                        <!-- Upload Icon -->
                        <div class="mx-auto w-16 h-16 mb-4 pointer-events-none">
                            <svg class="w-full h-full text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                      d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                            </svg>
                        </div>
                        
                        <!-- Upload Text -->
                        <div class="space-y-2 pointer-events-none">
                            <h3 class="text-lg font-semibold text-gray-800">Upload Test Image</h3>
                            <p class="text-gray-600">Drag and drop your image here, or click to browse</p>
                            <p class="text-sm text-gray-500">Supports: JPG, PNG, BMP, TIFF (Max 5MB)</p>
                        </div>
                        
                        <!-- File Input -->
                        <input type="file" 
                               name="image" 
                               id="image-input"
                               x-ref="fileInput"
                               accept="image/jpeg,image/jpg,image/png,image/bmp,image/tiff"
                               @change="handleFileSelect($event)"
                               class="absolute inset-0 w-full h-full opacity-0 cursor-pointer z-10"
                               required>
                    </div>

                    <!-- File Preview -->
                    <div x-show="selectedFile" 
                         x-transition:enter="transition ease-out duration-300"
                         x-transition:enter-start="opacity-0 transform scale-90"
                         x-transition:enter-end="opacity-100 transform scale-100"
                         class="mt-4 p-4 bg-gray-50 rounded-lg border">
                        <div class="flex items-center space-x-4">
                            <!-- Preview Image -->
                            <div class="flex-shrink-0">
                                <img x-show="previewUrl" 
                                     :src="previewUrl" 
                                     :alt="selectedFile?.name"
                                     class="w-20 h-20 object-cover rounded-lg border border-gray-200">
                            </div>
                            
                            <!-- File Info -->
                            <div class="flex-1 min-w-0">
                                <p class="text-sm font-medium text-gray-800 truncate" x-text="selectedFile?.name"></p>
                                <p class="text-sm text-gray-500" x-text="formatFileSize(selectedFile?.size)"></p>
                                <div class="mt-2">
                                    <div class="w-full bg-gray-200 rounded-full h-2">
                                        <div class="bg-red-500 h-2 rounded-full transition-all duration-300" 
                                             :style="`width: ${uploadProgress}%`"></div>
                                    </div>
                                    <p class="text-xs text-gray-500 mt-1" x-text="`${uploadProgress}% uploaded`"></p>
                                </div>
                            </div>
                            
                            <!-- Remove Button -->
                            <button type="button" 
                                    @click="removeFile()"
                                    class="flex-shrink-0 p-2 text-gray-400 hover:text-red-500 transition-colors">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Test Button -->
                <div class="flex justify-center">
                    <button type="submit" 
                            :disabled="!selectedFile"
                            class="px-8 py-3 bg-red-600 text-white font-semibold rounded-lg 
                                   hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed
                                   transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl">
                        Test Upload
                    </button>
                </div>
            </form>
        </div>
        
        <!-- Debug Info -->
        <div class="mt-8 bg-gray-800 text-green-400 p-4 rounded-lg font-mono text-sm" id="debug-info">
            <div>Debug Console:</div>
            <div id="debug-output">Ready for testing...</div>
        </div>
    </div>

    <script>
    function uploadForm() {
        return {
            selectedFile: null,
            previewUrl: null,
            isDragOver: false,
            uploadProgress: 0,
            
            init() {
                this.log('Upload form initialized');
            },
            
            handleFileSelect(event) {
                this.log('File select triggered');
                const file = event.target.files[0];
                if (file) {
                    this.log(`File selected: ${file.name} (${file.type}, ${file.size} bytes)`);
                    this.setFile(file);
                }
            },
            
            handleDragLeave(event) {
                const rect = event.currentTarget.getBoundingClientRect();
                const x = event.clientX;
                const y = event.clientY;
                
                if (x < rect.left || x >= rect.right || y < rect.top || y >= rect.bottom) {
                    this.isDragOver = false;
                    this.log('Drag left upload area');
                }
            },
            
            handleDrop(event) {
                event.preventDefault();
                event.stopPropagation();
                this.isDragOver = false;
                this.log('File dropped');
                
                const files = event.dataTransfer.files;
                if (files.length > 0) {
                    const file = files[0];
                    this.log(`Dropped file: ${file.name} (${file.type}, ${file.size} bytes)`);
                    const validation = this.validateFile(file);
                    if (validation.valid) {
                        this.setFile(file);
                        // Update the file input
                        const input = this.$refs.fileInput;
                        try {
                            const dt = new DataTransfer();
                            dt.items.add(file);
                            input.files = dt.files;
                            this.log('File input updated successfully');
                        } catch (e) {
                            this.log('DataTransfer not supported, file input not updated');
                        }
                    } else {
                        this.log(`Validation failed: ${validation.message}`);
                        alert(validation.message);
                    }
                }
            },
            
            validateFile(file) {
                const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/bmp', 'image/tiff'];
                const maxSize = 5 * 1024 * 1024; // 5MB
                
                if (!allowedTypes.includes(file.type)) {
                    return { valid: false, message: 'Please select a valid image file (JPG, PNG, BMP, TIFF)' };
                }
                
                if (file.size > maxSize) {
                    return { valid: false, message: 'File size must be less than 5MB' };
                }
                
                return { valid: true };
            },
            
            setFile(file) {
                const validation = this.validateFile(file);
                if (!validation.valid) {
                    this.log(`File validation failed: ${validation.message}`);
                    alert(validation.message);
                    return;
                }
                
                this.selectedFile = file;
                this.uploadProgress = 100;
                this.log(`File set successfully: ${file.name}`);
                
                // Create preview URL
                const reader = new FileReader();
                reader.onload = (e) => {
                    this.previewUrl = e.target.result;
                    this.log('Preview URL created');
                };
                reader.readAsDataURL(file);
            },
            
            removeFile() {
                this.log('File removed');
                this.selectedFile = null;
                this.previewUrl = null;
                this.uploadProgress = 0;
                this.$refs.fileInput.value = '';
            },
            
            handleSubmit(event) {
                if (!this.selectedFile) {
                    this.log('Submit blocked: No file selected');
                    alert('Please select an image to test');
                    return false;
                }
                this.log(`Form submitted with file: ${this.selectedFile.name}`);
                alert(`Test successful! File: ${this.selectedFile.name}`);
                return true;
            },
            
            formatFileSize(bytes) {
                if (bytes === 0) return '0 Bytes';
                const k = 1024;
                const sizes = ['Bytes', 'KB', 'MB', 'GB'];
                const i = Math.floor(Math.log(bytes) / Math.log(k));
                return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
            },
            
            log(message) {
                const output = document.getElementById('debug-output');
                const timestamp = new Date().toLocaleTimeString();
                output.innerHTML += `<br>[${timestamp}] ${message}`;
                output.scrollTop = output.scrollHeight;
                console.log(`[TomatoGuard] ${message}`);
            }
        }
    }
    
    // Global drag and drop prevention
    document.addEventListener('DOMContentLoaded', function() {
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            document.addEventListener(eventName, function(e) {
                e.preventDefault();
                e.stopPropagation();
            }, false);
        });
        
        console.log('Global drag and drop handlers attached');
    });
    </script>
</body>
</html>
