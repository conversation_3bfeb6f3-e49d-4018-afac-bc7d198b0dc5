#!/usr/bin/env python
"""
Simple test script to verify the upload functionality works.
Run this after setting up the Django project.
"""

import os
import sys
import django
from django.test import TestCase, Client
from django.core.files.uploadedfile import SimpleUploadedFile
from PIL import Image
import io

# Add the project directory to Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'TomatoGuard.settings')
django.setup()

def create_test_image():
    """Create a simple test image for upload testing."""
    # Create a simple RGB image
    img = Image.new('RGB', (300, 300), color='green')
    
    # Save to bytes
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='JPEG')
    img_bytes.seek(0)
    
    return SimpleUploadedFile(
        name='test_leaf.jpg',
        content=img_bytes.read(),
        content_type='image/jpeg'
    )

def test_upload_page():
    """Test that the upload page loads correctly."""
    client = Client()
    
    print("Testing upload page...")
    response = client.get('/upload/')
    
    if response.status_code == 200:
        print("✅ Upload page loads successfully")
        return True
    else:
        print(f"❌ Upload page failed with status {response.status_code}")
        return False

def test_form_validation():
    """Test form validation without actual Gemini API call."""
    from analyzer.forms import ImageUploadForm
    
    print("Testing form validation...")
    
    # Test with valid image
    test_image = create_test_image()
    form_data = {
        'analysis_type': 'disease_detection',
        'confidence_threshold': 75,
        'notes': 'Test upload'
    }
    file_data = {'image': test_image}
    
    form = ImageUploadForm(form_data, file_data)
    
    if form.is_valid():
        print("✅ Form validation passes with valid data")
        return True
    else:
        print(f"❌ Form validation failed: {form.errors}")
        return False

def test_image_processing():
    """Test image processing utilities."""
    from analyzer.utils.image_processing import validate_uploaded_image, preprocess_uploaded_image
    
    print("Testing image processing...")
    
    test_image = create_test_image()
    
    # Test validation
    is_valid, error_msg = validate_uploaded_image(test_image)
    
    if is_valid:
        print("✅ Image validation passes")
        
        # Test preprocessing
        try:
            test_image.seek(0)  # Reset file pointer
            processed_img = preprocess_uploaded_image(test_image)
            print(f"✅ Image preprocessing successful: {processed_img.size}")
            return True
        except Exception as e:
            print(f"❌ Image preprocessing failed: {e}")
            return False
    else:
        print(f"❌ Image validation failed: {error_msg}")
        return False

def main():
    """Run all tests."""
    print("🍅 TomatoGuard Upload Functionality Test")
    print("=" * 50)
    
    tests = [
        test_upload_page,
        test_form_validation,
        test_image_processing,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            print()
    
    print("=" * 50)
    print(f"Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Upload functionality is working.")
        print("\nNext steps:")
        print("1. Set up your Gemini API key in .env file")
        print("2. Run: python manage.py runserver")
        print("3. Visit: http://127.0.0.1:8000/upload/")
    else:
        print("⚠️  Some tests failed. Check the errors above.")
        print("\nTroubleshooting:")
        print("1. Make sure all dependencies are installed: pip install -r requirements.txt")
        print("2. Run migrations: python manage.py migrate")
        print("3. Check Django settings configuration")

if __name__ == '__main__':
    main()
